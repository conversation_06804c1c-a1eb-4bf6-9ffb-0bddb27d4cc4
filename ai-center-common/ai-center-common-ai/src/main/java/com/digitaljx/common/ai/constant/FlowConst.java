package com.digitaljx.common.ai.constant;

import dev.langchain4j.data.message.AiMessage;

/**
 * <AUTHOR>
 * @since 2025/3/4
 */
public class FlowConst {

    /**
     * 每块文档长度（按token算）
     */
    public static final int RAG_MAX_SEGMENT_SIZE_IN_TOKENS = 1000;

    /**
     * 文档召回默认数量
     */
    public static final int RAG_RETRIEVE_NUMBER_DEFAULT = 3;

    /**
     * 文档召回最大数量
     */
    public static final int RAG_RETRIEVE_NUMBER_MAX = 5;

    /**
     * 默认的最大输入token数
     */
    public static final int LLM_MAX_INPUT_TOKENS_DEFAULT = 4096;

    /**
     * 向量搜索时命中所需的最低分数
     */
    public static final double RAG_MIN_SCORE = 0.8;

    /**
     * 检索中断状态码
     */
    public static final int BREAK_SEARCH_MISSED_CODE = 9999;
    public static final String UN_KNOW_MESSAGE = "未匹配到答案，请换种问法。";
    public static final AiMessage UN_KNOW_AI_MESSAGE = new AiMessage(UN_KNOW_MESSAGE);

}
