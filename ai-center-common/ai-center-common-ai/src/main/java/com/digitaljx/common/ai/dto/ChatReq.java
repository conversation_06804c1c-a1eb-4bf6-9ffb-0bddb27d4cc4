package com.digitaljx.common.ai.dto;

import com.digitaljx.common.ai.utils.StreamEmitter;
import dev.langchain4j.model.input.Prompt;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@Data
@Accessors(chain = true)
public class ChatReq {

    private Long appId;
    private String modelId;
    private String modelName;
    private String modelProvider;

    private String message;

    private String conversationId;

    private Long userId;

    private String username;

    private String chatId;

    private String promptText;

    private String docsName;

    private String knowledgeId;
    private List<Long> knowledgeIds = new ArrayList<>();

    private String docsId;

    private String url;

    private String role;

    private Prompt prompt;

    private StreamEmitter emitter;
    private Executor executor;

    private String ip;
}
