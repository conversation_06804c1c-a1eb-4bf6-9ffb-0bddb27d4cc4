package com.digitaljx.common.ai.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@Slf4j
public class StreamEmitter {

    private final SseEmitter emitter;

    public StreamEmitter() {
        emitter = new SseEmitter(5 * 60 * 1000L);
    }

    public SseEmitter get() {
        return emitter;
    }

    public SseEmitter streaming(final ExecutorService executor, Runnable func) {
        emitter.onCompletion(() -> {
            log.info("SseEmitter 完成");
            executor.shutdownNow();
        });

        emitter.onError((e) -> {
            log.info("SseEmitter 出现错误: " + e.getMessage());
            executor.shutdownNow();
        });

        emitter.onTimeout(() -> {
            log.info("SseEmitter 超时");
            emitter.complete();
            executor.shutdownNow();
        });
        executor.execute(() -> {
            try {
                func.run();
            } catch (Exception e) {
                log.error("捕获到异常: {}", e.getMessage());
                emitter.completeWithError(e);
                Thread.currentThread().interrupt();
            } finally {
                if (!executor.isShutdown()) {
                    executor.shutdownNow();
                }
            }
        });
        return emitter;
    }

    public void send(Object obj) {
        try {
            emitter.send(obj);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public void complete() {
        emitter.complete();
    }

    public void error(String message) {
        try {
            emitter.send("Error: " + message);
            emitter.complete();
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
