package com.digitaljx.common.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/6/16
 */
@Getter
public enum ProviderEnum {

    OPENAI("OPENAI", "OpenAI"),
    AZURE_OPENAI("AZURE_OPENAI", "Azure OpenAI"),
    GEMINI("GEMINI", "Gemini"),
    OLLAMA("OLLAMA", "Ollama"),
    CLAUDE("CLAUDE", "Claude"),
    Q_FAN("Q_FAN", "千帆"),
    Q_WEN("Q_WEN", "通义千问"),
    ZHIPU("ZHIPU", "智谱"),
    YI("YI", "零一万物"),
    DOUYIN("DOUYIN", "豆包"),
    DEEPSEEK("DEEPSEEK", "深度求索"),
    SILICON("SILICON", "硅基流动"),
    SPARK("SPARK", "Spark"),
    COHERE("COHER<PERSON>",  "Cohere"),
    ;

    private final String code;
    private final String name;

    ProviderEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProviderEnum getByCode(String code) {
        for (ProviderEnum providerEnum : ProviderEnum.values()) {
            if (providerEnum.getCode().equals(code)) {
                return providerEnum;
            }
        }
        return null;
    }
}
