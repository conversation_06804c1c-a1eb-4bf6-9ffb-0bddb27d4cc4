<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ai-center</artifactId>
        <groupId>com.digitaljx</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>ai-center-common-bom</module>
        <module>ai-center-common-social</module>
        <module>ai-center-common-core</module>
        <module>ai-center-common-doc</module>
        <module>ai-center-common-excel</module>
        <module>ai-center-common-idempotent</module>
        <module>ai-center-common-job</module>
        <module>ai-center-common-log</module>
        <module>ai-center-common-mail</module>
        <module>ai-center-common-mybatis</module>
        <module>ai-center-common-oss</module>
        <module>ai-center-common-ratelimiter</module>
        <module>ai-center-common-redis</module>
        <module>ai-center-common-satoken</module>
        <module>ai-center-common-security</module>
        <module>ai-center-common-sms</module>
        <module>ai-center-common-web</module>
        <module>ai-center-common-translation</module>
        <module>ai-center-common-sensitive</module>
        <module>ai-center-common-json</module>
        <module>ai-center-common-encrypt</module>
        <module>ai-center-common-tenant</module>
        <module>ai-center-common-websocket</module>
        <module>ai-center-common-sse</module>
        <module>ai-center-common-ai</module>
    </modules>

    <artifactId>ai-center-common</artifactId>
    <packaging>pom</packaging>

    <description>
        common 通用模块
    </description>

</project>
