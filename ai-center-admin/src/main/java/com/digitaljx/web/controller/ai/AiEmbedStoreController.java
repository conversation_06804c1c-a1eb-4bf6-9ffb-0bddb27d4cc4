package com.digitaljx.web.controller.ai;

import java.util.List;

import com.digitaljx.common.core.constant.CacheConstants;
import com.digitaljx.common.redis.utils.RedisUtils;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.digitaljx.common.idempotent.annotation.RepeatSubmit;
import com.digitaljx.common.log.annotation.Log;
import com.digitaljx.common.web.core.BaseController;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.digitaljx.common.core.domain.R;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import com.digitaljx.common.log.enums.BusinessType;
import com.digitaljx.common.excel.utils.ExcelUtil;
import com.digitaljx.biz.domain.vo.AiEmbedStoreVo;
import com.digitaljx.biz.domain.bo.AiEmbedStoreBo;
import com.digitaljx.biz.service.IAiEmbedStoreService;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;

/**
 * 向量数据库
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/aigc/embedStore")
public class AiEmbedStoreController extends BaseController {

    private final IAiEmbedStoreService aiEmbedStoreService;

    /**
     * 查询向量数据库列表
     */
    @SaCheckPermission("biz:embedStore:list")
    @GetMapping("/list")
    public TableDataInfo<AiEmbedStoreVo> list(AiEmbedStoreBo bo, PageQuery pageQuery) {
        return aiEmbedStoreService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出向量数据库列表
     */
    @SaCheckPermission("biz:embedStore:export")
    @Log(title = "向量数据库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(AiEmbedStoreBo bo, HttpServletResponse response) {
        List<AiEmbedStoreVo> list = aiEmbedStoreService.queryList(bo);
        ExcelUtil.exportExcel(list, "向量数据库", AiEmbedStoreVo.class, response);
    }

    /**
     * 获取向量数据库详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("biz:embedStore:query")
    @GetMapping("/{id}")
    public R<AiEmbedStoreVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(aiEmbedStoreService.queryById(id));
    }

    /**
     * 新增向量数据库
     */
    @SaCheckPermission("biz:embedStore:add")
    @Log(title = "向量数据库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody AiEmbedStoreBo bo) {
        Boolean result = aiEmbedStoreService.insertByBo(bo);
//        log.info("通知刷新向量数据库缓存,id：{}", bo.getId());
//        RedisUtils.publish(CacheConstants.LOCAL_CACHE_EMBED_STORE, bo.getId());
        return toAjax(result);
    }

    /**
     * 修改向量数据库
     */
    @SaCheckPermission("biz:embedStore:edit")
    @Log(title = "向量数据库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody AiEmbedStoreBo bo) {
        Boolean result = aiEmbedStoreService.updateByBo(bo);
        log.info("通知刷新向量数据库缓存,id：{}", bo.getId());
        RedisUtils.publish(CacheConstants.LOCAL_CACHE_EMBED_STORE, bo.getId());
        return toAjax(result);
    }

    /**
     * 删除向量数据库
     *
     * @param ids 主键串
     */
    @SaCheckPermission("biz:embedStore:remove")
    @Log(title = "向量数据库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        Boolean result = aiEmbedStoreService.deleteWithValidByIds(List.of(ids), true);
        log.info("通知刷新向量数据库缓存,id：{}", ids[0]);
        RedisUtils.publish(CacheConstants.LOCAL_CACHE_EMBED_STORE, ids[0]);
        return toAjax(result);
    }
}
