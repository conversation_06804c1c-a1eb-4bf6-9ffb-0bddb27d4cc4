package com.digitaljx.api;

import com.digitaljx.ai.core.service.LangChainService;
import com.digitaljx.api.auth.CompletionReq;
import com.digitaljx.api.auth.CompletionRes;
import com.digitaljx.api.auth.OpenapiAuth;
import com.digitaljx.biz.domain.vo.AiAppApiVo;
import com.digitaljx.biz.domain.vo.AiAppVo;
import com.digitaljx.biz.service.IAiAppApiService;
import com.digitaljx.biz.service.IAiAppService;
import com.digitaljx.common.ai.dto.ChatReq;
import com.digitaljx.common.ai.utils.StreamEmitter;
import com.digitaljx.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;

import static com.digitaljx.common.core.constant.Constants.CHANNEL_API;

/**
 * 开放接口
 * <AUTHOR>
 * @since 2024/7/26
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1")
public class AppApiChatEndpoint {

    private final LangChainService langChainService;
    private final IAiAppService aiAppService;
    private final IAiAppApiService aiAppApiService;

    /**
     * AI聊天
     * @param req
     * @return
     */
    @OpenapiAuth(CHANNEL_API)
    @PostMapping(value = "/chat/completions")
    public SseEmitter completions(@RequestBody CompletionReq req) {
        StreamEmitter emitter = new StreamEmitter();
        AiAppApiVo aiAppApiVo = aiAppApiService.getByToken();
        return handler(emitter, aiAppApiVo.getAppId(), req.getMessages());
    }

    private SseEmitter handler(StreamEmitter emitter, Long appId, List<CompletionReq.Message> messages) {
        if (messages == null || messages.isEmpty() || appId != null) {
            throw new RuntimeException("聊天消息为空，或者没有配置模型信息");
        }
        CompletionReq.Message message = messages.get(0);

        AiAppVo app = aiAppService.getWithKnowledgeIds(appId);
        if (app == null) {
            throw new ServiceException("没有配置应用信息");
        }
        ChatReq req = new ChatReq()
                .setMessage(message.getContent())
                .setRole(message.getRole())
                .setModelId(String.valueOf(app.getModelId()))
                .setPromptText(app.getPrompt())
                .setKnowledgeIds(app.getKnowledgeIds());

        langChainService
                .singleChat(req)
                .onPartialResponse(token -> {
                    CompletionRes res = CompletionRes.process(token);
                    emitter.send(res);
                }).onCompleteResponse(c -> {
                    CompletionRes res = CompletionRes.end(c);
                    emitter.send(res);
                    emitter.complete();
                }).onError(e -> {
                    emitter.error(e.getMessage());
                }).start();

        return emitter.get();
    }
}
