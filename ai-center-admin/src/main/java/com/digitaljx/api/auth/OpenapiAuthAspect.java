package com.digitaljx.api.auth;

import com.digitaljx.biz.service.IAiAppApiService;
import com.digitaljx.common.core.exception.ServiceException;
import com.digitaljx.common.core.utils.ServletUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Aspect
@Configuration
@AllArgsConstructor
public class OpenapiAuthAspect {

    private final IAiAppApiService aiAppApiService;

    @Around("@annotation(openapiAuth)")
    public Object around(ProceedingJoinPoint point, OpenapiAuth openapiAuth) throws Throwable {
        String authorization = ServletUtils.getAuthorizationToken();
        if (authorization == null) {
            throw new ServiceException("Authentication Token invalid", 401);
        }

        String value = openapiAuth.value();
        Boolean expired = aiAppApiService.isExpired(value);
        if (expired) {
            throw new ServiceException("Authentication Token expired", 401);
        }
        return point.proceed();
    }

}
