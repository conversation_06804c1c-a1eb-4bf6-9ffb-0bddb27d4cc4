package com.digitaljx.service.impl;

import com.digitaljx.ai.core.service.LangChainService;
import com.digitaljx.biz.domain.AiMessage;
import com.digitaljx.biz.domain.vo.AiAppVo;
import com.digitaljx.biz.service.IAiAppService;
import com.digitaljx.biz.service.IAiMessageMongoService;
import com.digitaljx.common.ai.dto.ChatReq;
import com.digitaljx.common.ai.dto.ChatRes;
import com.digitaljx.common.ai.dto.ImageR;
import com.digitaljx.common.ai.enums.RoleEnum;
import com.digitaljx.common.ai.utils.StreamEmitter;
import com.digitaljx.service.ChatService;
import com.digitaljx.system.domain.vo.SysOssVo;
import dev.langchain4j.data.image.Image;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.model.output.TokenUsage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024/1/4
 */
@Slf4j
@Service
@AllArgsConstructor
public class ChatServiceImpl implements ChatService {

    private final LangChainService langChainService;
    private final IAiAppService aiAppService;
    private final IAiMessageMongoService aiMessageMongoService;

    @Override
    public void chat(ChatReq req) {
        StreamEmitter emitter = req.getEmitter();
        long startTime = System.currentTimeMillis();
        StringBuilder text = new StringBuilder();

        if (req.getAppId() != null) {
            AiAppVo app = aiAppService.getWithKnowledgeIds(req.getAppId());
            if (app != null) {
                req.setModelId(String.valueOf(app.getModelId()));
                req.setPromptText(app.getPrompt());
                req.setKnowledgeIds(app.getKnowledgeIds());
            }
        }

        // save user message
        req.setRole(RoleEnum.USER.getName());
        saveMessage(req, 0, 0);

        try {
            langChainService
                    .chat(req)
                    .onPartialResponse(e -> {
                        text.append(e);
                        emitter.send(new ChatRes(e));
                    })
                    .onCompleteResponse((e) -> {
                        TokenUsage tokenUsage = e.tokenUsage();
                        ChatRes res = new ChatRes(tokenUsage.totalTokenCount(), startTime);
                        emitter.send(res);
                        emitter.complete();

                        // save assistant message
                        req.setMessage(text.toString());
                        req.setRole(RoleEnum.ASSISTANT.getName());
                        saveMessage(req, tokenUsage.inputTokenCount(), tokenUsage.outputTokenCount());
                    })
                    .onError((e) -> {
                        emitter.error(e.getMessage());
                        throw new RuntimeException(e.getMessage());
                    })
                    .start();
        } catch (Exception e) {
            log.error("聊天异常", e);
            emitter.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    private void saveMessage(ChatReq req, Integer inputToken, Integer outputToken) {
//        if (req.getConversationId() != null) {
            AiMessage message = new AiMessage();
            BeanUtils.copyProperties(req, message);
            message.setIp(req.getIp());
            message.setModel(req.getModelName());
            message.setPromptTokens(inputToken);
            message.setTokens(outputToken);
            message.setContent(req.getMessage());
            message.setCreateTime(LocalDateTime.now());
            aiMessageMongoService.save(message);
//        }
    }

    @Override
    public String text(ChatReq req) {
        String text;
        try {
            text = langChainService.text(req);
        } catch (Exception e) {
            log.error("聊天异常", e);
            throw new RuntimeException(e.getMessage());
        }
        return text;
    }

    @Override
    public SysOssVo image(ImageR req) {
        Response<Image> res = langChainService.image(req);
        String path = res.content().url().toString();
        SysOssVo oss = new SysOssVo();
        oss.setUrl(path);
        return oss;
    }
}
