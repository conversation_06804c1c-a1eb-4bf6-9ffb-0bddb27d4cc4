package com.digitaljx.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.digitaljx.ai.core.provider.EmbeddingProvider;
import com.digitaljx.ai.core.service.LangEmbeddingService;
import com.digitaljx.biz.domain.AiDocs;
import com.digitaljx.biz.domain.AiDocsSlice;
import com.digitaljx.biz.domain.bo.AiDocsBo;
import com.digitaljx.biz.mapper.AiDocsMapper;
import com.digitaljx.biz.service.IAiKnowledgeService;
import com.digitaljx.common.ai.dto.ChatReq;
import com.digitaljx.common.ai.dto.EmbeddingR;
import com.digitaljx.service.EmbeddingService;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.filter.Filter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.digitaljx.common.ai.constant.EmbedConst.KNOWLEDGE;
import static dev.langchain4j.store.embedding.filter.MetadataFilterBuilder.metadataKey;

/**
 * <AUTHOR>
 * @since 2024/6/6
 */
@Slf4j
@Service
@AllArgsConstructor
public class EmbeddingServiceImpl implements EmbeddingService {

    private final EmbeddingProvider embeddingProvider;
    private final LangEmbeddingService langEmbeddingService;
    private final IAiKnowledgeService aiKnowledgeService;
    private final AiDocsMapper aiDocsMapper;

    @Override
    @Transactional
    public void clearDocSlices(String docsId) {
        if (StrUtil.isBlank(docsId)) {
            return;
        }
        // remove from embedding store
        List<String> vectorIds = aiKnowledgeService.listSliceVectorIdsOfDoc(Long.valueOf(docsId));
        if (vectorIds.isEmpty()) {
            return;
        }
        AiDocs docs = aiDocsMapper.selectById(docsId);
        EmbeddingStore<TextSegment> embeddingStore = embeddingProvider.getEmbeddingStore(docs.getKnowledgeId());
        embeddingStore.removeAll(vectorIds);
        // remove from docSlice
        aiKnowledgeService.removeSlicesOfDoc(docsId);
    }

    @Override
    public void embedDocsSlice(AiDocs data, String url) {
        List<EmbeddingR> list = langEmbeddingService.embeddingDocs(
                new ChatReq()
                        .setDocsName(data.getName())
                        .setKnowledgeId(String.valueOf(data.getKnowledgeId()))
                        .setUrl(url));
        list.forEach(i -> {
            aiKnowledgeService.addDocsSlice(new AiDocsSlice()
                    .setKnowledgeId(data.getKnowledgeId())
                    .setDocsId(data.getId())
                    .setVectorId(i.getVectorId())
                    .setName(data.getName())
                    .setContent(i.getText())
            );
        });

        aiDocsMapper.update(null, new LambdaUpdateWrapper<AiDocs>()
            .eq(AiDocs::getId, data.getId())
            .set(AiDocs::getSliceStatus, true)
            .set(AiDocs::getSliceNum, list.size()));
    }

    @Override
    public List<Map<String, Object>> search(AiDocsBo data) {
        if (data.getKnowledgeId() == null || StrUtil.isBlank(data.getContent())) {
            return List.of();
        }

        EmbeddingModel embeddingModel = embeddingProvider.getEmbeddingModel(data.getKnowledgeId());
        EmbeddingStore<TextSegment> embeddingStore = embeddingProvider.getEmbeddingStore(data.getKnowledgeId());
        Embedding queryEmbedding = embeddingModel.embed(data.getContent()).content();
        Filter filter = metadataKey(KNOWLEDGE).isEqualTo(data.getKnowledgeId());
        EmbeddingSearchResult<TextSegment> list = embeddingStore.search(EmbeddingSearchRequest
                .builder()
                .queryEmbedding(queryEmbedding)
                .filter(filter)
                .build());

        List<Map<String, Object>> result = new ArrayList<>();
        list.matches().forEach(i -> {
            TextSegment embedded = i.embedded();
            Map<String, Object> map = embedded.metadata().toMap();
            map.put("text", embedded.text());
            result.add(map);
        });
        return result;
    }
}
