package com.digitaljx.endpoint;

import cn.hutool.core.thread.ThreadUtil;
import com.digitaljx.biz.domain.AiMessage;
import com.digitaljx.biz.domain.vo.AiAppVo;
import com.digitaljx.biz.domain.vo.AiModelVo;
import com.digitaljx.biz.service.IAiAppService;
import com.digitaljx.biz.service.IAiMessageMongoService;
import com.digitaljx.biz.service.IAiModelService;
import com.digitaljx.common.ai.dto.ChatReq;
import com.digitaljx.common.ai.dto.ChatRes;
import com.digitaljx.common.ai.dto.ImageR;
import com.digitaljx.common.ai.dto.PromptConst;
import com.digitaljx.common.ai.utils.PromptUtil;
import com.digitaljx.common.ai.utils.StreamEmitter;
import com.digitaljx.common.core.domain.R;
import com.digitaljx.common.core.utils.ServletUtils;
import com.digitaljx.common.satoken.utils.LoginHelper;
import com.digitaljx.service.ChatService;
import com.digitaljx.system.domain.vo.SysOssVo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 聊天接口
 * <AUTHOR>
 * @since 2024/1/30
 */
@Slf4j
@RequestMapping("/aigc")
@RestController
@AllArgsConstructor
public class ChatEndpoint {

    private final ChatService chatService;
    private final IAiModelService aigcModelService;
    private final IAiAppService aiAppService;
    private final IAiMessageMongoService aiMessageMongoService;

    @PostMapping("/chat/completions")
//    @SaCheckPermission("chat:completions")
    public SseEmitter chat(@RequestBody ChatReq req) {
        Long userId = LoginHelper.getUserId();
        String username = LoginHelper.getUsername();
        StreamEmitter emitter = new StreamEmitter();
        req.setEmitter(emitter);
        req.setUserId(userId);
        req.setUsername(username);
        String ip = ServletUtils.getClientIP();
        req.setIp(ip);
        ExecutorService executor = ThreadUtil.newSingleExecutor();
        req.setExecutor(executor);
        return emitter.streaming(executor, () -> chatService.chat(req));
    }

    @GetMapping("/app/info")
    public R<AiAppVo> appInfo(@RequestParam Long appId, String conversationId) {
        AiAppVo app = aiAppService.queryById(appId);
        return R.ok(app);
    }

    @GetMapping("/chat/messages/{conversationId}")
    public R<List<AiMessage>> messages(@PathVariable String conversationId) {
        List<AiMessage> list = aiMessageMongoService.lambdaQuery().eq(AiMessage::getConversationId, conversationId)
            .eq(AiMessage::getUserId, LoginHelper.getUserId())
            .list();
        return R.ok(list);
    }

    @DeleteMapping("/chat/messages/clean/{conversationId}")
//    @SaCheckPermission("chat:messages:clean")
    public R<Void> cleanMessage(@PathVariable String conversationId) {
        aiMessageMongoService.removeByColumn(AiMessage::getConversationId, conversationId);
        return R.ok();
    }

    @PostMapping("/chat/mindmap")
    public R<ChatRes> mindmap(@RequestBody ChatReq req) {
        req.setPrompt(PromptUtil.build(req.getMessage(), PromptConst.MINDMAP));
        return R.ok(new ChatRes(chatService.text(req)));
    }

    @PostMapping("/chat/image")
    public R<SysOssVo> image(@RequestBody ImageR req) {
        req.setPrompt(PromptUtil.build(req.getMessage(), PromptConst.IMAGE));
        SysOssVo ossVo = chatService.image(req);
        return R.ok(ossVo);
    }

    @GetMapping("/chat/getImageModels")
    public R<List<AiModelVo>> getImageModels() {
        List<AiModelVo> list = aigcModelService.getImageModels();
        list.forEach(i -> {
            i.setApiKey(null);
            i.setSecretKey(null);
        });
        return R.ok(list);
    }

}
