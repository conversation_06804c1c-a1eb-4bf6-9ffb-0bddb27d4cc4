package com.digitaljx.endpoint;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.digitaljx.biz.domain.bo.AiDocsBo;
import com.digitaljx.biz.service.IAiDocsService;
import com.digitaljx.common.ai.constant.EmbedConst;
import com.digitaljx.ai.core.service.LangEmbeddingService;
import com.digitaljx.biz.domain.AiDocs;
import com.digitaljx.biz.domain.AiDocsSlice;
import com.digitaljx.biz.mapper.AiDocsMapper;
import com.digitaljx.biz.mapper.AiDocsSliceMapper;
import com.digitaljx.biz.service.IAiKnowledgeService;
import com.digitaljx.common.ai.dto.ChatReq;
import com.digitaljx.common.ai.dto.EmbeddingR;
import com.digitaljx.common.core.domain.R;
import com.digitaljx.common.core.exception.ServiceException;
import com.digitaljx.common.satoken.utils.LoginHelper;
import com.digitaljx.service.EmbeddingService;
import com.digitaljx.system.domain.vo.SysOssVo;
import com.digitaljx.system.service.ISysOssService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 知识库接口
 * <AUTHOR>
 * @since 2024/4/25
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/aigc/embedding")
public class EmbeddingEndpoint {

    private final LangEmbeddingService langEmbeddingService;
    private final IAiKnowledgeService aiKnowledgeService;
    private final IAiDocsService aiDocsService;
    private final AiDocsMapper aiDocsMapper;
    private final AiDocsSliceMapper aiDocsSliceMapper;
    private final EmbeddingService embeddingService;
    private final ISysOssService ossService;

    /**
     * 知识库手动输入文档
     * @param bo
     * @return
     */
    @PostMapping("/text")
    public R<Void> text(@Validated @RequestBody AiDocsBo bo) {
        if (StrUtil.isBlankIfStr(bo.getContent())) {
            throw new ServiceException("文档内容不能为空");
        }
        if (bo.getId() == null) {
            bo.setType(EmbedConst.ORIGIN_TYPE_INPUT);
            bo.setSliceStatus(false);
            aiDocsService.insertByBo(bo);
        }

        try {
            EmbeddingR embeddingR = langEmbeddingService.embeddingText(
                    new ChatReq().setMessage(bo.getContent())
                            .setDocsName(bo.getType())
                            .setDocsId(String.valueOf(bo.getId()))
                            .setKnowledgeId(String.valueOf(bo.getKnowledgeId())));

            aiKnowledgeService.addDocsSlice(new AiDocsSlice()
                    .setKnowledgeId(bo.getKnowledgeId())
                    .setDocsId(bo.getId())
                    .setVectorId(embeddingR.getVectorId())
                    .setName(bo.getName())
                    .setContent(embeddingR.getText())
            );
            aiDocsMapper.update(null, new LambdaUpdateWrapper<AiDocs>()
                    .set(AiDocs::getSliceStatus, true)
                    .set(AiDocs::getSliceNum, 1)
                    .eq(AiDocs::getId, bo.getId()));
        } catch (Exception e) {
            log.error("文档处理异常", e);

            // del data
            int rows = aiDocsSliceMapper.delete(new LambdaQueryWrapper<AiDocsSlice>()
                .eq(AiDocsSlice::getDocsId, bo.getId()));
            log.debug("remove all slices of doc: [{}], count: [{}]", bo.getId(), rows);
        }
        return R.ok();
    }

    /**
     * 知识库导入文档
     * @param file
     * @param knowledgeId
     * @return
     */
    @PostMapping("/docs/{knowledgeId}")
//    @SaCheckPermission("aigc:embedding:docs")
    public R<Void> docs(@RequestPart("file") MultipartFile file, @PathVariable Long knowledgeId) {
        String extension = FileUtil.extName(file.getOriginalFilename());

        // FIXME：校验格式
//        if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION)) {
//            return R.fail("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
//        }
        SysOssVo oss = ossService.upload(file);

        AiDocs data = new AiDocs()
                .setName(oss.getOriginalName())
                .setSliceStatus(false)
                .setUrl(oss.getUrl())
                .setFileSize(file.getSize())
                .setType(EmbedConst.ORIGIN_TYPE_UPLOAD)
                .setKnowledgeId(knowledgeId);
        aiDocsMapper.insert(data);
        ThreadUtil.execute(() ->
            embeddingService.embedDocsSlice(data, oss.getUrl())
        );
        return R.ok();
    }

    @GetMapping("/re-embed/{docsId}")
    public R<Void> reEmbed(@PathVariable String docsId) {
        Long userId = LoginHelper.getUserId();
        AiDocs docs = aiDocsMapper.selectById(docsId);
        if (docs == null) {
            return R.fail("没有查询到文档数据");
        }
        if (EmbedConst.ORIGIN_TYPE_INPUT.equals(docs.getType())) {
            AiDocsBo bo = BeanUtil.toBean(docs, AiDocsBo.class);
            text(bo);
        } else if (EmbedConst.ORIGIN_TYPE_UPLOAD.equals(docs.getType())) {
            // clear before re-embed
            embeddingService.clearDocSlices(docsId);
            ThreadUtil.execute(() ->
                embeddingService.embedDocsSlice(docs, docs.getUrl()));
        }
        return R.ok();
    }

    @PostMapping("/search")
    public R<List<Map<String, Object>>> search(@RequestBody AiDocsBo data) {
        return R.ok(embeddingService.search(data));
    }
}
