<template>
  <div class="container">
    <!--面包屑-->
    <Breadcrumb :items="['这里手动改掉', '这里手动改掉']"/>
    <a-card
      class="general-card r-nav-card"
      :body-style="{ padding: '15px' }"
      :header-style="{ padding: '15px' }"
    >
      <a-row>
        <!-- 搜索区块 -->
        <a-col :flex="1">
          <a-form
            v-show="showSearch"
            ref="queryRef"
            :model="queryParams"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
            auto-label-width
          >
            <a-row :gutter="16">
      #foreach($column in $columns)
      #if($column.query)
              <a-col :span="8">
        #set($dictType=$column.dictType)
        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
        #set($parentheseIndex=$column.columnComment.indexOf("（"))
        #if($parentheseIndex != -1)
        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
        #else
        #set($comment=$column.columnComment)
        #end
        #if($column.htmlType == "input" || $column.htmlType == "textarea")
              <a-form-item label="${comment}" field="${column.javaField}">
                <a-input
                  v-model="queryParams.${column.javaField}"
                  placeholder="请输入${comment}"
                  allow-clear
                  @keyup.enter="handleQuery"
                />
              </a-form-item>
        #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
              <a-form-item label="${comment}" field="${column.javaField}">
                <a-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" allow-clear>
                  <a-option
                    v-for="dict in ${dictType}"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="dict.dictValue"
                  />
                </a-select>
              </a-form-item>
        #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
              <a-form-item label="${comment}" field="${column.javaField}">
                <a-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" allow-clear>
                  <a-option label="请选择字典生成" value="" />
                </a-select>
              </a-form-item>
        #elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
              <a-form-item label="${comment}" field="${column.javaField}">
                <a-date-picker allow-clear
                  v-model="queryParams.${column.javaField}"
                  value-format="YYYY-MM-DD"
                  placeholder="选择${comment}">
                </a-date-picker>
              </a-form-item>
        #elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
              <a-form-item label="${comment}" style="width: 308px">
                <a-range-picker
                  v-model="daterange${AttrName}"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始日期','结束日期']"
                ></a-date-picker>
              </a-form-item>
        #end
              </a-col>
      #end
      #end
            </a-row>
          </a-form>
        </a-col>
        <a-divider style="height: 42px" direction="vertical" />
        <a-col :flex="'86px'" style="text-align: right">
          <a-space :size="18">
            <a-button type="primary" @click="handleQuery">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="resetQuery">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
        <a-divider style="margin-top: 0" />

        <a-row :gutter="10" class="mb8">
          <a-col :span="1.5">
            <a-button
              v-permission="['${moduleName}:${businessName}:add']"
              type="primary"
              @click="handleAdd"
            >
              <template #icon>
                <icon-plus />
              </template>
              新增
            </a-button>
          </a-col>
          <a-col :span="1.5">
            <a-button
              @click="toggleExpandAll"
            >
              <template #icon>
                <icon-shrink v-if="isExpandAll" />
                <icon-expand v-if="!isExpandAll" />
              </template>
              <span v-if="isExpandAll">折叠</span>
              <span v-if="!isExpandAll">展开</span>
            </a-button>
          </a-col>
        </a-row>

      </a-row>
      <a-spin :loading="loading" style="width: 100%;">
        <a-table
          v-if="refreshTable"
          ref="tableRef"
          :data="${businessName}List"
          row-key="${treeCode}"
          :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        >
          <template #columns>
    #foreach($column in $columns)
    #set($javaField=$column.javaField)
    #set($parentheseIndex=$column.columnComment.indexOf("（"))
    #if($parentheseIndex != -1)
    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
    #else
    #set($comment=$column.columnComment)
    #end
    #if($column.pk)
    #elseif($column.list && $column.htmlType == "datetime")
          <a-table-column title="${comment}" align="center" data-index="${javaField}">
            <template #cell="{ record }">
              <span>{{ ${javaField} ? dayjs(record.${javaField}).format('YYYY-MM-DD') : '--' }}</span>
            </template>
          </a-table-column>
    #elseif($column.list && $column.htmlType == "imageUpload")
          <a-table-column title="${comment}" align="center" data-index="${javaField}">
              <template #cell="{ record }">
                  <image-preview :src="record.${javaField}" :height="50"/>
              </template>
          </a-table-column>
    #elseif($column.list && $column.dictType && "" != $column.dictType)
          <a-table-column title="${comment}" align="center" data-index="${javaField}">
            <template #cell="{ record }">
    #if($column.htmlType == "checkbox")
              <dict-tag :options="${column.dictType}" :value="record.${javaField} ? record.${javaField}.split(',') : []"/>
    #else
              <dict-tag :options="${column.dictType}" :value="record.${javaField}"/>
    #end
            </template>
          </a-table-column>
    #elseif($column.list && "" != $javaField)
    #if(${foreach.index} == 1)
          <a-table-column title="${comment}" data-index="${javaField}" />
    #else
          <a-table-column title="${comment}" align="center" data-index="${javaField}" />
    #end
    #end
    #end
          <a-table-column title="操作" align="center" class-name="small-padding fixed-width">
              <template #cell="{ record }">
                  <a-button v-permission="['${moduleName}:${businessName}:edit']" type="text" @click="handleUpdate(record)">
                    <template #icon>
                      <icon-edit />
                    </template>
                    修改
                  </a-button>
                  <a-button v-permission="['${moduleName}:${businessName}:add']" type="text" @click="handleAdd(record)">
                    <template #icon>
                      <icon-plus />
                    </template>
                    新增
                  </a-button>
                  <a-button v-permission="['${moduleName}:${businessName}:remove']" type="text" @click="handleDelete(record)">
                    <template #icon>
                      <icon-delete />
                    </template>
                    删除
                  </a-button>
              </template>
          </a-table-column>
          </template>
        </a-table>
      </a-spin>
    </a-card>

    <!-- 添加或修改${functionName}对话框 -->
    <a-modal v-model:visible="open" :title="title" width="500px" render-to-body>
      <a-form ref="${businessName}Ref" :model="form" auto-label-width :rules="rules">
#foreach($column in $columns)
#set($field=$column.javaField)
#if($column.insert && !$column.pk)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#set($dictType=$column.dictType)
#if("" != $treeParentCode && $column.javaField == $treeParentCode)
        <a-form-item label="${comment}" field="${treeParentCode}">
          <a-tree-select
            v-model="form.${treeParentCode}"
            :data="${businessName}Options"
            :field-names="{ key: '${treeCode}', title: '${treeName}', children: 'children' }"
            placeholder="请选择${comment}"
          />
        </a-form-item>
#elseif($column.htmlType == "input")
        <a-form-item label="${comment}" field="${field}">
          <a-input v-model="form.${field}" placeholder="请输入${comment}" />
        </a-form-item>
#elseif($column.htmlType == "imageUpload")
        <a-form-item label="${comment}" field="${field}">
          <image-upload v-model="form.${field}"/>
        </a-form-item>
#elseif($column.htmlType == "fileUpload")
        <a-form-item label="${comment}" field="${field}">
          <file-upload v-model="form.${field}"/>
        </a-form-item>
#elseif($column.htmlType == "editor")
        <a-form-item label="${comment}">
          <editor v-model="form.${field}" :min-height="192"/>
        </a-form-item>
#elseif($column.htmlType == "select" && "" != $dictType)
        <a-form-item label="${comment}" field="${field}">
          <a-select v-model="form.${field}" placeholder="请选择${comment}">
            <a-option
              v-for="dict in ${dictType}"
              :key="dict.dictValue"
              :label="dict.dictLabel"
#if($column.javaType == "Integer" || $column.javaType == "Long")
              :value="parseInt(dict.dictValue)"
#else
              :value="dict.dictValue"
#end
            ></a-option>
          </a-select>
        </a-form-item>
#elseif($column.htmlType == "select" && $dictType)
        <a-form-item label="${comment}" field="${field}">
          <a-select v-model="form.${field}" placeholder="请选择${comment}">
            <a-option label="请选择字典生成" value="" />
          </a-select>
        </a-form-item>
#elseif($column.htmlType == "checkbox" && "" != $dictType)
        <a-form-item label="${comment}" field="${field}">
          <a-checkbox-group v-model="form.${field}">
            <a-checkbox
              v-for="dict in ${dictType}"
              :key="dict.dictValue"
              :value="dict.dictValue">
              {{dict.dictLabel}}
            </a-checkbox>
          </a-checkbox-group>
        </a-form-item>
#elseif($column.htmlType == "checkbox" && $dictType)
        <a-form-item label="${comment}" field="${field}">
          <a-checkbox-group v-model="form.${field}">
            <a-checkbox>请选择字典生成</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
#elseif($column.htmlType == "radio" && "" != $dictType)
        <a-form-item label="${comment}" field="${field}">
          <a-radio-group v-model="form.${field}">
            <a-radio
              v-for="dict in ${dictType}"
              :key="dict.dictValue"
#if($column.javaType == "Integer" || $column.javaType == "Long")
              :value="parseInt(dict.dictValue)"
#else
              :value="dict.dictValue"
#end
            >{{dict.dictLabel}}</a-radio>
          </a-radio-group>
        </a-form-item>
#elseif($column.htmlType == "radio" && $dictType)
        <a-form-item label="${comment}" field="${field}">
          <a-radio-group v-model="form.${field}">
            <a-radio value="1">请选择字典生成</a-radio>
          </a-radio-group>
        </a-form-item>
#elseif($column.htmlType == "datetime")
        <a-form-item label="${comment}" field="${field}">
          <a-date-picker
            allow-clear
            v-model="form.${field}"
            show-time
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="选择${comment}">
          </a-date-picker>
        </a-form-item>
#elseif($column.htmlType == "textarea")
        <a-form-item label="${comment}" field="${field}">
          <a-textarea v-model="form.${field}" placeholder="请输入内容" />
        </a-form-item>
#end
#end
#end
      </a-form>
      <template #footer>
        <a-space class="dialog-footer">
          <a-button type="primary" @click="submitForm">确 定</a-button>
          <a-button @click="cancel">取 消</a-button>
        </a-space>
      </template>
    </a-modal>
  </div>
</template>

<script setup name="${BusinessName}">
import dayjs from 'dayjs';
import { Message, Modal } from '@arco-design/web-vue';
#if(${dicts} != '')
import DictTag from "@/components/dict-tag/index.vue";
#end
import { list${BusinessName}, get${BusinessName}, del${BusinessName}, add${BusinessName}, update${BusinessName} } from "@/api/${moduleName}/${businessName}";

const { proxy } = getCurrentInstance();
#if(${dicts} != '')
#set($dictsNoSymbol=$dicts.replace("'", ""))
const { ${dictsNoSymbol} } = proxy.useDict(${dicts});
#end

const ${businessName}List = ref([]);
const ${businessName}Options = ref([]);
const open = ref(false);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const isExpandAll = ref(true);
const refreshTable = ref(true);
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
const daterange${AttrName} = ref([]);
#end
#end

const data = reactive({
  form: {},
  queryParams: {
#foreach ($column in $columns)
#if($column.query)
    $column.javaField: undefined#if($foreach.count != $columns.size()),#end
#end
#end
  },
  rules: {
#foreach ($column in $columns)
#if($column.required)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
    $column.javaField: [
      { required: true, message: "$comment不能为空", trigger: #if($column.htmlType == "select" || $column.htmlType == "radio")"change"#else"blur"#end }
    ]#if($foreach.count != $columns.size()),#end
#end
#end
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询${functionName}列表 */
function getList() {
  loading.value = true;
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
  queryParams.value.params = {};
#break
#end
#end
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
  if (null != daterange${AttrName} && '' != daterange${AttrName}) {
    queryParams.value.params["begin${AttrName}"] = daterange${AttrName}.value[0];
    queryParams.value.params["end${AttrName}"] = daterange${AttrName}.value[1];
  }
#end
#end
  list${BusinessName}(queryParams.value).then(response => {
    ${businessName}List.value = proxy.handleTree(response.data, "${treeCode}", "${treeParentCode}");
    loading.value = false;
  });
}

/** 查询${functionName}下拉树结构 */
function getTreeselect() {
  list${BusinessName}().then(response => {
    ${businessName}Options.value = [];
    const mainNode = { ${treeCode}: 0, ${treeName}: '顶级节点', children: [] };
    mainNode.children = proxy.handleTree(response.data, "${treeCode}", "${treeParentCode}");
    ${businessName}Options.value.push(data);
  });
}

// 表单重置
function reset() {
  form.value = {
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
    $column.javaField: []#if($foreach.count != $columns.size()),#end
#else
    $column.javaField: null#if($foreach.count != $columns.size()),#end
#end
#end
  };
  proxy.resetForm("${businessName}Ref");
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
#foreach ($column in $columns)
#if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
#set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
  daterange${AttrName}.value = [];
#end
#end
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  getTreeselect();
  if (row != null && row.${treeCode}) {
    form.value.${treeParentCode} = row.${treeParentCode};
  } else {
    form.value.${treeParentCode} = 0;
  }
  open.value = true;
  title.value = "添加${functionName}";
}

// 页面表格元素选择
const tableRef = ref();
// 展开/折叠操作
const toggleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
  if (tableRef.value) {
    tableRef.value.expandAll(isExpandAll.value);
  }
};

/** 修改按钮操作 */
async function handleUpdate(row) {
  loading.value = true;
  reset();
  await getTreeselect();
  if (row != null) {
    form.value.${treeParentCode} = row.${treeCode};
  }
  get${BusinessName}(row.${pkColumn.javaField}).then(response => {
    loading.value = false;
    form.value = response.data;
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
    form.value.$column.javaField = form.value.${column.javaField}.split(",");
#end
#end
    open.value = true;
    title.value = "修改${functionName}";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.#[[$]]#refs.${businessName}Ref.validate(valid => {
    if (!valid) {
      buttonLoading.value = true;
#foreach ($column in $columns)
#if($column.htmlType == "checkbox")
      form.value.$column.javaField = form.value.${column.javaField}.join(",");
#end
#end
      if (form.value.${pkColumn.javaField} != null) {
        update${BusinessName}(form.value).then(response => {
          Message.success("修改成功");
          open.value = false;
          getList();
        }).finally(() => {
          buttonLoading.value = false;
        });
      } else {
        add${BusinessName}(form.value).then(response => {
          Message.success("新增成功");
          open.value = false;
          getList();
        }).finally(() => {
          buttonLoading.value = false;
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  Modal.confirm({
    content: '是否确认删除${functionName}编号为"' + row.${pkColumn.javaField} + '"的数据项？',
    onOk: () => {
        loading.value = true;
        del${BusinessName}(row.${pkColumn.javaField})
        .then(() => {
            loading.value = false;
            getList();
            Message.success("删除成功");
        }).catch(() => {
        }).finally(() => {
            loading.value = false;
        });
    },
  });
}

getList();
</script>
