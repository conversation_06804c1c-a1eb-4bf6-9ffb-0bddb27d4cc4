<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.digitaljx</groupId>
        <artifactId>ai-center-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ai-center-system</artifactId>

    <description>
        system系统模块
    </description>

    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-translation</artifactId>
        </dependency>

        <!-- OSS功能模块 -->
        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-log</artifactId>
        </dependency>

        <!-- excel-->
        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-excel</artifactId>
        </dependency>

        <!-- SMS功能模块 -->
        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-idempotent</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-sensitive</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-encrypt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.digitaljx</groupId>
            <artifactId>ai-center-common-sse</artifactId>
        </dependency>

    </dependencies>

</project>
