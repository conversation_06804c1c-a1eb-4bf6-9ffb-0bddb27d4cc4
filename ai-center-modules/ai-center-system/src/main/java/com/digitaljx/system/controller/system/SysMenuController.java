package com.digitaljx.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import lombok.RequiredArgsConstructor;
import com.digitaljx.common.core.constant.SystemConstants;
import com.digitaljx.common.core.constant.TenantConstants;
import com.digitaljx.common.core.domain.R;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.log.annotation.Log;
import com.digitaljx.common.log.enums.BusinessType;
import com.digitaljx.common.redis.utils.RedisUtils;
import com.digitaljx.common.satoken.utils.LoginHelper;
import com.digitaljx.common.web.core.BaseController;
import com.digitaljx.system.domain.SysMenu;
import com.digitaljx.system.domain.bo.SysMenuBo;
import com.digitaljx.system.domain.cmd.SysMenuCollectCmd;
import com.digitaljx.system.domain.dto.SysMenuDTO;
import com.digitaljx.system.domain.vo.MenuTreeSelectVo;
import com.digitaljx.system.domain.vo.RouterVo;
import com.digitaljx.system.domain.vo.SysMenuVo;
import com.digitaljx.system.mapper.SysMenuMapper;
import com.digitaljx.system.service.ISysMenuService;
import org.redisson.api.RScoredSortedSet;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单信息
 *
 * <AUTHOR> Li
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/menu")
public class SysMenuController extends BaseController {

    private final ISysMenuService menuService;
    private static final String COLLECT_KEY = "home_menu_collects:";
    private final SysMenuMapper sysMenuMapper;

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("/getRouters")
    public R<List<RouterVo>> getRouters() {
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(LoginHelper.getUserId());
        return R.ok(menuService.buildMenus(menus));
    }

    /**
     * 获取菜单列表
     */
    @SaCheckRole(value = {
            TenantConstants.SUPER_ADMIN_ROLE_KEY,
            TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:menu:list")
    @GetMapping("/list")
    public R<List<SysMenuVo>> list(SysMenuBo menu) {
        List<SysMenuVo> menus = menuService.selectMenuList(menu, LoginHelper.getUserId());
        return R.ok(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     *
     * @param menuId 菜单ID
     */
    @SaCheckRole(value = {
            TenantConstants.SUPER_ADMIN_ROLE_KEY,
            TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:menu:query")
    @GetMapping(value = "/{menuId}")
    public R<SysMenuVo> getInfo(@PathVariable Long menuId) {
        return R.ok(menuService.selectMenuById(menuId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @SaCheckPermission("system:menu:query")
    @GetMapping("/treeselect")
    public R<List<Tree<Long>>> treeselect(SysMenuBo menu) {
        List<SysMenuVo> menus = menuService.selectMenuList(menu, LoginHelper.getUserId());
        return R.ok(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     *
     * @param roleId 角色ID
     */
    @SaCheckPermission("system:menu:query")
    @GetMapping(value = "/roleMenuTreeselect/{roleId}")
    public R<MenuTreeSelectVo> roleMenuTreeselect(@PathVariable("roleId") Long roleId) {
        List<SysMenuVo> menus = menuService.selectMenuList(LoginHelper.getUserId());
        MenuTreeSelectVo selectVo = new MenuTreeSelectVo();
        selectVo.setCheckedKeys(menuService.selectMenuListByRoleId(roleId));
        selectVo.setMenus(menuService.buildMenuTreeSelect(menus));
        return R.ok(selectVo);
    }

    /**
     * 加载对应租户套餐菜单列表树
     *
     * @param packageId 租户套餐ID
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:menu:query")
    @GetMapping(value = "/tenantPackageMenuTreeselect/{packageId}")
    public R<MenuTreeSelectVo> tenantPackageMenuTreeselect(@PathVariable("packageId") Long packageId) {
        List<SysMenuVo> menus = menuService.selectMenuList(LoginHelper.getUserId());
        MenuTreeSelectVo selectVo = new MenuTreeSelectVo();
        selectVo.setCheckedKeys(menuService.selectMenuListByPackageId(packageId));
        selectVo.setMenus(menuService.buildMenuTreeSelect(menus));
        return R.ok(selectVo);
    }

    /**
     * 新增菜单
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:menu:add")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysMenuBo menu) {
        if (!menuService.checkMenuNameUnique(menu)) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (SystemConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        }
        return toAjax(menuService.insertMenu(menu));
    }

    /**
     * 修改菜单
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:menu:edit")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysMenuBo menu) {
        if (!menuService.checkMenuNameUnique(menu)) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (SystemConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        } else if (menu.getMenuId().equals(menu.getParentId())) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        }
        return toAjax(menuService.updateMenu(menu));
    }

    /**
     * 删除菜单
     *
     * @param menuId 菜单ID
     */
    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    @SaCheckPermission("system:menu:remove")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    public R<Void> remove(@PathVariable("menuId") Long menuId) {
        if (menuService.hasChildByMenuId(menuId)) {
            return R.warn("存在子菜单,不允许删除");
        }
        if (menuService.checkMenuExistRole(menuId)) {
            return R.warn("菜单已分配,不允许删除");
        }
        return toAjax(menuService.deleteMenuById(menuId));
    }

    /**
     * 收藏列表
     */
    @GetMapping("/collects")
    public R<List<SysMenuDTO>> collects() {
        Long userId = LoginHelper.getUserId();
        String key = COLLECT_KEY + userId;
        RScoredSortedSet<Long> sortedSet = RedisUtils.getClient().getScoredSortedSet(key);
        if (sortedSet == null) {
            return R.ok(Collections.emptyList());
        }
        Collection<Long> cacheMenuIds = sortedSet.valueRange(0, true, System.currentTimeMillis(), true);
        if (CollUtil.isEmpty(cacheMenuIds)) {
            return R.ok(Collections.emptyList());
        }
        List<SysMenu> sysMenus = sysMenuMapper.selectByIds(cacheMenuIds);
        List<SysMenuDTO> dtos = sysMenus.stream()
            .map(it -> {
                SysMenuDTO dto = BeanUtil.toBean(it, SysMenuDTO.class);
                dto.setName(it.getRouteName());
                dto.setPath(it.getRouterPath());
                return dto;
            })
            .collect(Collectors.toList());
        return R.ok(dtos);
    }

    /**
     * 添加收藏
     */
    @PostMapping("/collect/add")
    public R<Void> addCollect(@Validated @RequestBody SysMenuCollectCmd cmd) {
        Long userId = LoginHelper.getUserId();
        String key = COLLECT_KEY + userId;
        RedisUtils.getClient().getScoredSortedSet(key).add(System.currentTimeMillis(), cmd.getMenuId());
        return R.ok();
    }

    /**
     * 删除收藏
     */
    @PostMapping("/collect/remove")
    public R<Void> removeCollect(@Validated @RequestBody SysMenuCollectCmd cmd) {
        Long userId = LoginHelper.getUserId();
        String key = COLLECT_KEY + userId;
        RedisUtils.getClient().getScoredSortedSet(key).remove(cmd.getMenuId());
        return R.ok();
    }

}
