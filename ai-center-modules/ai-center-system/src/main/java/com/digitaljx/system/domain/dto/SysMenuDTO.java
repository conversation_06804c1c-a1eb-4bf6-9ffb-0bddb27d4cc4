package com.digitaljx.system.domain.dto;

import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/10/10
 */
@Data
public class SysMenuDTO {

    private Long menuId;

    /**
     * 路由名字
     */
    private String name;

    private String menuName;

    private String path;

    /**
     * 菜单图标
     */
    private String icon;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SysMenuDTO that = (SysMenuDTO) o;
        return Objects.equals(menuId, that.menuId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(menuId);
    }
}
