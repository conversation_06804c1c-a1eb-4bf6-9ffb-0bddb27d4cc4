package com.digitaljx.system.controller.system;

import lombok.RequiredArgsConstructor;
import com.digitaljx.common.core.domain.R;
import com.digitaljx.common.satoken.utils.LoginHelper;
import com.digitaljx.common.web.core.BaseController;
import com.digitaljx.system.domain.vo.SysSocialVo;
import com.digitaljx.system.service.ISysSocialService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 社会化关系
 *
 * <AUTHOR>
 * @date 2023-06-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/social")
public class SysSocialController extends BaseController {

    private final ISysSocialService socialUserService;

    /**
     * 查询社会化关系列表
     */
    @GetMapping("/list")
    public R<List<SysSocialVo>> list() {
        return R.ok(socialUserService.queryListByUserId(LoginHelper.getUserId()));
    }

}
