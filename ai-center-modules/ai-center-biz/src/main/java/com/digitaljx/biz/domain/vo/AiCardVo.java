package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiCard;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 卡片视图对象 ai_card
 *
 * <AUTHOR> Li
 * @date 2025-02-26
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiCard.class)
public class AiCardVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 卡片名称
     */
    @ExcelProperty(value = "卡片名称")
    private String cardName;

    /**
     * 卡片编码
     */
    @ExcelProperty(value = "卡片编码")
    private String cardCode;

    /**
     * 卡片类型
     */
    @ExcelProperty(value = "卡片类型")
    private String cardType;

    /**
     * 卡片信息
     */
    @ExcelProperty(value = "卡片信息")
    private String cardInfo;

    /**
     * 进入方式:1-自动进入;2-手动进入
     */
    @ExcelProperty(value = "进入方式:1-自动进入;2-手动进入")
    private String entryType;

    /**
     * 卡片状态
     */
    @ExcelProperty(value = "卡片状态")
    private String cardStatus;


}
