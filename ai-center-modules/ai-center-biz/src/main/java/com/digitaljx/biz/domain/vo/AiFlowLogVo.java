package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiFlowLog;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 流程日志视图对象 ai_flow_log
 *
 * <AUTHOR> Li
 * @date 2025-03-05
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiFlowLog.class)
public class AiFlowLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 流程ID
     */
    @ExcelProperty(value = "流程ID")
    private Long flowId;

    /**
     * 类型 0-失败 1-成功 2-未知
     */
    @ExcelProperty(value = "类型 0-失败 1-成功 2-未知")
    private Long type;

    /**
     * 传入参数
     */
    @ExcelProperty(value = "传入参数")
    private String inputParams;

    /**
     * 结果
     */
    @ExcelProperty(value = "结果")
    private String result;

    /**
     * 节点运行信息
     */
    @ExcelProperty(value = "节点运行信息")
    private String nodeInfo;


}
