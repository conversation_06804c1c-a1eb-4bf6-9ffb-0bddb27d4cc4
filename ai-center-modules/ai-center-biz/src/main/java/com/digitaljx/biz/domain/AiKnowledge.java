package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 知识库对象 ai_knowledge
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_knowledge")
public class AiKnowledge extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 向量数据库ID
     */
    private Long embedStoreId;

    /**
     * 向量模型ID
     */
    private Long embedModelId;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 描述
     */
    private String des;

    /**
     * 封面
     */
    private String coverImg;

    /**
     * 排序模型ID
     */
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long scoringModelId;

}
