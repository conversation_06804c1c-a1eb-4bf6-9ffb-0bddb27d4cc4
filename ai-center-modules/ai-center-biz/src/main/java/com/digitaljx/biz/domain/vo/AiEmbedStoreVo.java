package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiEmbedStore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 向量数据库视图对象 ai_embed_store
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiEmbedStore.class)
public class AiEmbedStoreVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 别名
     */
    @ExcelProperty(value = "别名")
    private String name;

    /**
     * 供应商
     */
    @ExcelProperty(value = "供应商")
    private String provider;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String host;

    /**
     * 端口
     */
    @ExcelProperty(value = "端口")
    private Integer port;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    private String username;

    /**
     * 密码
     */
    @ExcelProperty(value = "密码")
    private String password;

    /**
     * 数据库名称
     */
    @ExcelProperty(value = "数据库名称")
    private String databaseName;

    /**
     * 表名称
     */
    @ExcelProperty(value = "表名称")
    private String tableName;

    /**
     * 向量维数
     */
    @ExcelProperty(value = "向量维数")
    private Integer dimension;


}
