package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiDocs;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 文档业务对象 ai_docs
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiDocs.class, reverseConvertGenerate = false)
public class AiDocsBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long knowledgeId;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     *
     */
    private String url;

    /**
     * 来源
     */
    private String origin;

    /**
     * 内容或链接
     */
    private String content;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 切片数量
     */
    private Integer sliceNum;

    /**
     * 切片状态
     */
    private Boolean sliceStatus;


}
