package com.digitaljx.biz.domain.bo;

import com.baomidou.mybatisplus.annotation.Version;
import com.digitaljx.biz.domain.AiApp;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;
import java.util.List;

/**
 * 应用业务对象 ai_app
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiApp.class, reverseConvertGenerate = false)
public class AiAppBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 关联模型
     */
    private Long modelId;

    /**
     * 关联知识库
     */
    private List<Long> knowledgeIds;

    /**
     * 封面
     */
    private String coverImg;

    /**
     * 名称
     */
    private String name;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 描述
     */
    private String des;

    /**
     * 流程
     */
    private String flow;

    /**
     * 应用类型
     * CHAT-聊天助手 WORKFLOW-工作流
     */
    private String appType;

    /**
     * 标签（可以根据标签调用）
     */
    private String label;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 版本
     */
    @Version
    private Integer version;

    /**
     * 草稿
     */
    private String draft;

    /**
     * 数据
     */
    private String data;

    /**
     * 发布时间
     */
    private Date releaseTime;

}
