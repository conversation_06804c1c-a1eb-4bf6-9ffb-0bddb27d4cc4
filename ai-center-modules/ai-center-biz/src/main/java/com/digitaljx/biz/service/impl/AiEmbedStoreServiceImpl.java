package com.digitaljx.biz.service.impl;

import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiEmbedStoreBo;
import com.digitaljx.biz.domain.vo.AiEmbedStoreVo;
import com.digitaljx.biz.domain.AiEmbedStore;
import com.digitaljx.biz.mapper.AiEmbedStoreMapper;
import com.digitaljx.biz.service.IAiEmbedStoreService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 向量数据库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@RequiredArgsConstructor
@Service
public class AiEmbedStoreServiceImpl implements IAiEmbedStoreService {

    private final AiEmbedStoreMapper baseMapper;

    /**
     * 查询向量数据库
     *
     * @param id 主键
     * @return 向量数据库
     */
    @Override
    public AiEmbedStoreVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询向量数据库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 向量数据库分页列表
     */
    @Override
    public TableDataInfo<AiEmbedStoreVo> queryPageList(AiEmbedStoreBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiEmbedStore> lqw = buildQueryWrapper(bo);
        Page<AiEmbedStoreVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的向量数据库列表
     *
     * @param bo 查询条件
     * @return 向量数据库列表
     */
    @Override
    public List<AiEmbedStoreVo> queryList(AiEmbedStoreBo bo) {
        LambdaQueryWrapper<AiEmbedStore> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiEmbedStore> buildQueryWrapper(AiEmbedStoreBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiEmbedStore> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiEmbedStore::getId);
        lqw.like(StringUtils.isNotBlank(bo.getName()), AiEmbedStore::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getProvider()), AiEmbedStore::getProvider, bo.getProvider());
//        lqw.eq(StringUtils.isNotBlank(bo.getHost()), AiEmbedStore::getHost, bo.getHost());
        lqw.eq(bo.getPort() != null, AiEmbedStore::getPort, bo.getPort());
        lqw.like(StringUtils.isNotBlank(bo.getUsername()), AiEmbedStore::getUsername, bo.getUsername());
        lqw.eq(StringUtils.isNotBlank(bo.getPassword()), AiEmbedStore::getPassword, bo.getPassword());
        lqw.like(StringUtils.isNotBlank(bo.getDatabaseName()), AiEmbedStore::getDatabaseName, bo.getDatabaseName());
        lqw.like(StringUtils.isNotBlank(bo.getTableName()), AiEmbedStore::getTableName, bo.getTableName());
        lqw.eq(bo.getDimension() != null, AiEmbedStore::getDimension, bo.getDimension());
        return lqw;
    }

    /**
     * 新增向量数据库
     *
     * @param bo 向量数据库
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiEmbedStoreBo bo) {
        AiEmbedStore add = MapstructUtils.convert(bo, AiEmbedStore.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改向量数据库
     *
     * @param bo 向量数据库
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiEmbedStoreBo bo) {
        AiEmbedStore update = MapstructUtils.convert(bo, AiEmbedStore.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiEmbedStore entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除向量数据库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
