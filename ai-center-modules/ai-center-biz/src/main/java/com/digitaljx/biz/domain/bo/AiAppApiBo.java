package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiAppApi;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 应用渠道业务对象 ai_app_api
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiAppApi.class, reverseConvertGenerate = false)
public class AiAppApiBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 应用渠道
     */
    private String channel;

    /**
     * Key
     */
    private String apiKey;

}
