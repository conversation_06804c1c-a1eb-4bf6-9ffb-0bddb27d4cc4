package com.digitaljx.biz.service.impl;

import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiFlowLogBo;
import com.digitaljx.biz.domain.vo.AiFlowLogVo;
import com.digitaljx.biz.domain.AiFlowLog;
import com.digitaljx.biz.mapper.AiFlowLogMapper;
import com.digitaljx.biz.service.IAiFlowLogService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 流程日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@RequiredArgsConstructor
@Service
public class AiFlowLogServiceImpl implements IAiFlowLogService {

    private final AiFlowLogMapper baseMapper;

    /**
     * 查询流程日志
     *
     * @param id 主键
     * @return 流程日志
     */
    @Override
    public AiFlowLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询流程日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 流程日志分页列表
     */
    @Override
    public TableDataInfo<AiFlowLogVo> queryPageList(AiFlowLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiFlowLog> lqw = buildQueryWrapper(bo);
        Page<AiFlowLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的流程日志列表
     *
     * @param bo 查询条件
     * @return 流程日志列表
     */
    @Override
    public List<AiFlowLogVo> queryList(AiFlowLogBo bo) {
        LambdaQueryWrapper<AiFlowLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiFlowLog> buildQueryWrapper(AiFlowLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiFlowLog> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiFlowLog::getId);
        lqw.eq(bo.getFlowId() != null, AiFlowLog::getFlowId, bo.getFlowId());
        lqw.eq(bo.getType() != null, AiFlowLog::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getInputParams()), AiFlowLog::getInputParams, bo.getInputParams());
        lqw.eq(StringUtils.isNotBlank(bo.getResult()), AiFlowLog::getResult, bo.getResult());
        lqw.eq(StringUtils.isNotBlank(bo.getNodeInfo()), AiFlowLog::getNodeInfo, bo.getNodeInfo());
        return lqw;
    }

    /**
     * 新增流程日志
     *
     * @param bo 流程日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiFlowLogBo bo) {
        AiFlowLog add = MapstructUtils.convert(bo, AiFlowLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改流程日志
     *
     * @param bo 流程日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiFlowLogBo bo) {
        AiFlowLog update = MapstructUtils.convert(bo, AiFlowLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiFlowLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除流程日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
