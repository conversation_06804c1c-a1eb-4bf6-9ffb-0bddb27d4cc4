package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 模型对象 ai_model
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_model")
public class AiModel extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 类型: chat、embedding、image
     */
    private String type;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 供应商
     */
    private String provider;

    /**
     * 别名
     */
    private String alias;

    /**
     * 响应长度
     */
    private Integer responseLimit;

    /**
     * 温度
     */
    private Double temperature;

    /**
     *
     */
    private Double topP;

    /**
     *
     */
    private String apiKey;

    /**
     *
     */
    private String baseUrl;

    /**
     *
     */
    private String secretKey;

    /**
     *
     */
    private String endpoint;

    /**
     * azure模型参数
     */
    private String azureDeploymentName;

    /**
     * gemini模型参数
     */
    private String geminiProject;

    /**
     * gemini模型参数
     */
    private String geminiLocation;

    /**
     * 图片大小
     */
    private String imageSize;

    /**
     * 图片质量
     */
    private String imageQuality;

    /**
     * 图片风格
     */
    private String imageStyle;

    /**
     * 向量维数
     */
    private Integer dimension;


}
