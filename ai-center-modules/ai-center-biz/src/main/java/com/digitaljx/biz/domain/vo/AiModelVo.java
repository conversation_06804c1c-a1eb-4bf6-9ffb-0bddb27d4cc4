package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiModel;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 模型视图对象 ai_model
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiModel.class)
public class AiModelVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 类型: chat、embedding、image
     */
    @ExcelProperty(value = "类型: chat、embedding、image")
    private String type;

    /**
     * 模型名称
     */
    @ExcelProperty(value = "模型名称")
    private String modelName;

    /**
     * 供应商
     */
    @ExcelProperty(value = "供应商")
    private String provider;

    /**
     * 别名
     */
    @ExcelProperty(value = "别名")
    private String alias;

    /**
     * 响应长度
     */
    @ExcelProperty(value = "响应长度")
    private Integer responseLimit;

    /**
     * 温度
     */
    @ExcelProperty(value = "温度")
    private Double temperature;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Double topP;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String apiKey;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String baseUrl;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String secretKey;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String endpoint;

    /**
     * azure模型参数
     */
    @ExcelProperty(value = "azure模型参数")
    private String azureDeploymentName;

    /**
     * gemini模型参数
     */
    @ExcelProperty(value = "gemini模型参数")
    private String geminiProject;

    /**
     * gemini模型参数
     */
    @ExcelProperty(value = "gemini模型参数")
    private String geminiLocation;

    /**
     * 图片大小
     */
    @ExcelProperty(value = "图片大小")
    private String imageSize;

    /**
     * 图片质量
     */
    @ExcelProperty(value = "图片质量")
    private String imageQuality;

    /**
     * 图片风格
     */
    @ExcelProperty(value = "图片风格")
    private String imageStyle;

    /**
     * 向量维数
     */
    @ExcelProperty(value = "向量维数")
    private Integer dimension;


}
