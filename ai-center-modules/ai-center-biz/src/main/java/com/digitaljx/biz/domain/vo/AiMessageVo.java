package com.digitaljx.biz.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;

import com.digitaljx.biz.domain.AiMessage;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;


/**
 * 消息视图对象 ai_message
 *
 * <AUTHOR> Li
 * @date 2025-02-18
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiMessage.class)
public class AiMessageVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 会话ID
     */
    @ExcelProperty(value = "会话ID")
    private Long conversationId;

    /**
     * 消息的ID
     */
    @ExcelProperty(value = "消息的ID")
    private String chatId;

    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名")
    private String username;

    /**
     * IP地址
     */
    @ExcelProperty(value = "IP地址")
    private String ip;

    /**
     * 角色，user和assistant
     */
    @ExcelProperty(value = "角色，user和assistant")
    private String role;

    /**
     * 模型名称
     */
    @ExcelProperty(value = "模型名称")
    private String model;

    /**
     * 消息内容
     */
    @ExcelProperty(value = "消息内容")
    private String content;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer tokens;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Integer promptTokens;

    /**
     * 创建时间
     */
    private Date createTime;

}
