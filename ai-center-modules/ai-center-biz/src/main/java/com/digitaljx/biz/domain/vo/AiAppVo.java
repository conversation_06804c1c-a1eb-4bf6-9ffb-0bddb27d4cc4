package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiApp;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 应用视图对象 ai_app
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiApp.class)
public class AiAppVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 关联模型
     */
    @ExcelProperty(value = "关联模型")
    private Long modelId;

    /**
     * 封面
     */
    @ExcelProperty(value = "封面")
    private String coverImg;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 提示词
     */
    @ExcelProperty(value = "提示词")
    private String prompt;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String des;

    /**
     * 流程
     */
    private String flow;

    /**
     * 应用类型
     * CHAT-聊天助手 WORKFLOW-工作流
     */
    private String appType;


    /**
     * 模型
     */
    private AiModelVo model;


    /**
     * 关联知识库
     */
    private List<Long> knowledgeIds;

    /**
     * 知识库
     */
    private List<AiKnowledgeVo> knowledges = new ArrayList<>();

    /**
     * 标签（可以根据标签调用）
     */
    private String label;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 草稿
     */
    private String draft;

    /**
     * 数据
     */
    private String data;

    /**
     * 发布时间
     */
    private Date releaseTime;

}
