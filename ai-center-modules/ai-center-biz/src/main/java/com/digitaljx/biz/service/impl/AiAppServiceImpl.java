package com.digitaljx.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.digitaljx.biz.domain.AiAppKnowledge;
import com.digitaljx.biz.domain.AiKnowledge;
import com.digitaljx.biz.mapper.AiAppKnowledgeMapper;
import com.digitaljx.biz.mapper.AiKnowledgeMapper;
import com.digitaljx.biz.mapper.AiModelMapper;
import com.digitaljx.common.core.constant.CacheNames;
import com.digitaljx.common.core.exception.ServiceException;
import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiAppBo;
import com.digitaljx.biz.domain.vo.AiAppVo;
import com.digitaljx.biz.domain.AiApp;
import com.digitaljx.biz.mapper.AiAppMapper;
import com.digitaljx.biz.service.IAiAppService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 应用Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@RequiredArgsConstructor
@Service
public class AiAppServiceImpl implements IAiAppService {

    private final AiAppMapper baseMapper;
    private final AiModelMapper aiModelMapper;
    private final AiKnowledgeMapper aiKnowledgeMapper;
    private final AiAppKnowledgeMapper aiAppKnowledgeMapper;

    /**
     * 查询应用
     *
     * @param id 主键
     * @return 应用
     */
    @Override
    public AiAppVo queryById(Long id) {
        AiAppVo app = baseMapper.selectVoById(id);
        if (app == null) {
            return null;
        }

        if (app.getModelId() != null) {
            app.setModel(aiModelMapper.selectVoById(app.getModelId()));
        }

        List<Long> knowledgeIds = aiAppKnowledgeMapper.selectKnowledgeIdsByAppId(app.getId());
        if (CollUtil.isNotEmpty(knowledgeIds)) {
            app.setKnowledgeIds(knowledgeIds);
            app.setKnowledges(aiKnowledgeMapper.selectVoList(Wrappers.<AiKnowledge>lambdaQuery()
                .in(AiKnowledge::getId, knowledgeIds)));
        }

        return app;
    }

    @Override
    @Cacheable(cacheNames = CacheNames.AI_APP, key = "#id")
    public AiAppVo getWithKnowledgeIds(Long id) {
        AiAppVo app = baseMapper.selectVoById(id);
        List<Long> knowledgeIds = aiAppKnowledgeMapper.selectKnowledgeIdsByAppId(app.getId());
        app.setKnowledgeIds(knowledgeIds);
        return app;
    }

    /**
     * 分页查询应用列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应用分页列表
     */
    @Override
    public TableDataInfo<AiAppVo> queryPageList(AiAppBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiApp> lqw = buildQueryWrapper(bo);
        Page<AiAppVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应用列表
     *
     * @param bo 查询条件
     * @return 应用列表
     */
    @Override
    public List<AiAppVo> queryList(AiAppBo bo) {
        LambdaQueryWrapper<AiApp> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiApp> buildQueryWrapper(AiAppBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiApp> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getModelId() != null, AiApp::getModelId, bo.getModelId());
//        lqw.eq(StringUtils.isNotBlank(bo.getKnowledgeIds()), AiApp::getKnowledgeIds, bo.getKnowledgeIds());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverImg()), AiApp::getCoverImg, bo.getCoverImg());
        lqw.like(StringUtils.isNotBlank(bo.getName()), AiApp::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getPrompt()), AiApp::getPrompt, bo.getPrompt());
        lqw.eq(StringUtils.isNotBlank(bo.getDes()), AiApp::getDes, bo.getDes());
        lqw.orderByDesc(AiApp::getUpdateTime);
        return lqw;
    }

    /**
     * 新增应用
     *
     * @param bo 应用
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiAppBo bo) {
        AiApp add = MapstructUtils.convert(bo, AiApp.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            if (CollUtil.isNotEmpty(bo.getKnowledgeIds())) {
                aiAppKnowledgeMapper.insertBatch(bo.getKnowledgeIds().stream()
                    .map(it -> {
                        AiAppKnowledge aiAppKnowledge = new AiAppKnowledge();
                        aiAppKnowledge.setAppId(bo.getId());
                        aiAppKnowledge.setKnowledgeId(it);
                        return aiAppKnowledge;
                    }).collect(Collectors.toList()));
            }
        }
        return flag;
    }

    /**
     * 修改应用
     *
     * @param bo 应用
     * @return 是否修改成功
     */
    @Override
    @CachePut(cacheNames = CacheNames.AI_APP, key = "#bo.id")
    public Boolean updateByBo(AiAppBo bo) {
        AiApp update = MapstructUtils.convert(bo, AiApp.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiApp entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除应用信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @CacheEvict(cacheNames = CacheNames.AI_APP, allEntries = true)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @CachePut(cacheNames = CacheNames.AI_APP, key = "#appId")
    public Boolean release(Long appId) {
        AiApp app = baseMapper.selectById(appId);
        if (app == null) {
            throw new ServiceException("应用不存在");
        }
        app.setVersion(app.getVersion() + 1);
        app.setData(app.getDraft());
        app.setReleaseTime(new Date());
        return baseMapper.updateById(app) > 0;
    }

    @Override
    @CachePut(cacheNames = CacheNames.AI_APP, key = "#aiAppKnowledge.appId")
    public Boolean addKnowledge(AiAppKnowledge aiAppKnowledge) {
        return aiAppKnowledgeMapper.insert(aiAppKnowledge) > 0;
    }

    @Override
    @CachePut(cacheNames = CacheNames.AI_APP, key = "#aiAppKnowledge.appId")
    public Boolean removeKnowledge(AiAppKnowledge aiAppKnowledge) {
        return aiAppKnowledgeMapper.delete(new LambdaQueryWrapper<AiAppKnowledge>()
            .eq(AiAppKnowledge::getAppId, aiAppKnowledge.getAppId())
            .eq(AiAppKnowledge::getKnowledgeId, aiAppKnowledge.getKnowledgeId())) > 0;
    }
}
