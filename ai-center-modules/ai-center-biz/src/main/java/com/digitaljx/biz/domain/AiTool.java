package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工具对象 ai_tool
 *
 * <AUTHOR> Li
 * @date 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_tool")
public class AiTool extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 工具类型: 系统/自定义
     */
    private String toolType;

    /**
     * 工具编码
     */
    private String toolCode;

    /**
     * 参数,JSON格式
     */
    private String feature;

    /**
     * 备注
     */
    private String remark;


}
