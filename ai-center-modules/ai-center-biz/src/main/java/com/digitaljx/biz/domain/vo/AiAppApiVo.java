package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiAppApi;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 应用渠道视图对象 ai_app_api
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiAppApi.class)
public class AiAppApiVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 应用ID
     */
    @ExcelProperty(value = "应用ID")
    private Long appId;

    /**
     * 应用渠道
     */
    @ExcelProperty(value = "应用渠道")
    private String channel;

    /**
     * Key
     */
    @ExcelProperty(value = "Key")
    private String apiKey;


}
