package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiMessage;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 消息业务对象 ai_message
 *
 * <AUTHOR> Li
 * @date 2025-02-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiMessage.class, reverseConvertGenerate = false)
public class AiMessageBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 消息的ID
     */
    private String chatId;

    /**
     * 用户名
     */
    private String username;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 角色，user和assistant
     */
    private String role;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 消息内容
     */
    private String content;

    /**
     *
     */
    private Integer tokens;

    /**
     *
     */
    private Integer promptTokens;


}
