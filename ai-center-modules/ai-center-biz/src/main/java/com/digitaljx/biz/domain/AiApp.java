package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 应用对象 ai_app
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_app")
public class AiApp extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 关联模型
     */
    private Long modelId;

    /**
     * 封面
     */
    private String coverImg;

    /**
     * 名称
     */
    private String name;

    /**
     * 提示词
     */
    private String prompt;

    /**
     * 描述
     */
    private String des;

    /**
     * 应用类型
     * CHAT-聊天助手 WORKFLOW-工作流
     */
    private String appType;

    /**
     * 标签（可以根据标签调用）
     */
    private String label;

    /**
     * 状态 0-禁用 1-启用
     */
    private Integer status;

    /**
     * 版本
     */
    @Version
    private Integer version;

    /**
     * 草稿
     */
    private String draft;

    /**
     * 数据
     */
    private String data;

    /**
     * 发布时间
     */
    private Date releaseTime;

}
