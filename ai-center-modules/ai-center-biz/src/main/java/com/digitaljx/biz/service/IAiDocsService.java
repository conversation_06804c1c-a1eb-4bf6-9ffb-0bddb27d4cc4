package com.digitaljx.biz.service;

import com.digitaljx.biz.domain.vo.AiDocsVo;
import com.digitaljx.biz.domain.bo.AiDocsBo;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 文档Service接口
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
public interface IAiDocsService {

    /**
     * 查询文档
     *
     * @param id 主键
     * @return 文档
     */
    AiDocsVo queryById(Long id);

    /**
     * 分页查询文档列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 文档分页列表
     */
    TableDataInfo<AiDocsVo> queryPageList(AiDocsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的文档列表
     *
     * @param bo 查询条件
     * @return 文档列表
     */
    List<AiDocsVo> queryList(AiDocsBo bo);

    /**
     * 新增文档
     *
     * @param bo 文档
     * @return 是否新增成功
     */
    Boolean insertByBo(AiDocsBo bo);

    /**
     * 修改文档
     *
     * @param bo 文档
     * @return 是否修改成功
     */
    Boolean updateByBo(AiDocsBo bo);

    /**
     * 校验并批量删除文档信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
