package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 流程日志对象 ai_flow_log
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_flow_log")
public class AiFlowLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 流程ID
     */
    private Long flowId;

    /**
     * 类型 0-失败 1-成功 2-未知
     */
    private Integer type;

    /**
     * 传入参数
     */
    private String inputParams;

    /**
     * 结果
     */
    private String result;

    /**
     * 节点运行信息
     */
    private String nodeInfo;


}
