package com.digitaljx.biz.service.impl;

import com.digitaljx.biz.domain.*;
import com.digitaljx.biz.mapper.AiDocsSliceMapper;
import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiKnowledgeBo;
import com.digitaljx.biz.domain.vo.AiKnowledgeVo;
import com.digitaljx.biz.mapper.AiKnowledgeMapper;
import com.digitaljx.biz.service.IAiKnowledgeService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 知识库Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AiKnowledgeServiceImpl implements IAiKnowledgeService {

    private final AiKnowledgeMapper baseMapper;
    private final AiDocsSliceMapper aiDocsSliceMapper;

    /**
     * 查询知识库
     *
     * @param id 主键
     * @return 知识库
     */
    @Override
    public AiKnowledgeVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询知识库列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 知识库分页列表
     */
    @Override
    public TableDataInfo<AiKnowledgeVo> queryPageList(AiKnowledgeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiKnowledge> lqw = buildQueryWrapper(bo);
        Page<AiKnowledgeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的知识库列表
     *
     * @param bo 查询条件
     * @return 知识库列表
     */
    @Override
    public List<AiKnowledgeVo> queryList(AiKnowledgeBo bo) {
        LambdaQueryWrapper<AiKnowledge> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiKnowledge> buildQueryWrapper(AiKnowledgeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiKnowledge> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiKnowledge::getId);
        lqw.eq(bo.getUserId() != null, AiKnowledge::getUserId, bo.getUserId());
        lqw.eq(bo.getEmbedStoreId() != null, AiKnowledge::getEmbedStoreId, bo.getEmbedStoreId());
        lqw.eq(bo.getEmbedModelId() != null, AiKnowledge::getEmbedModelId, bo.getEmbedModelId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), AiKnowledge::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getDes()), AiKnowledge::getDes, bo.getDes());
        lqw.eq(StringUtils.isNotBlank(bo.getCoverImg()), AiKnowledge::getCoverImg, bo.getCoverImg());
        return lqw;
    }

    /**
     * 新增知识库
     *
     * @param bo 知识库
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiKnowledgeBo bo) {
        AiKnowledge add = MapstructUtils.convert(bo, AiKnowledge.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改知识库
     *
     * @param bo 知识库
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiKnowledgeBo bo) {
        AiKnowledge update = MapstructUtils.convert(bo, AiKnowledge.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiKnowledge entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除知识库信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<String> listSliceVectorIdsOfDoc(Long docsId) {
        LambdaQueryWrapper<AiDocsSlice> selectWrapper = Wrappers.<AiDocsSlice>lambdaQuery()
            .select(AiDocsSlice::getVectorId)
            .eq(AiDocsSlice::getDocsId, docsId);
        List<String> vectorIds = aiDocsSliceMapper.selectList(selectWrapper)
            .stream()
            .map(String::valueOf)
            .toList();
        log.debug("slices of doc: [{}], count: [{}]", docsId, vectorIds.size());
        return vectorIds;
    }

    @Override
    public void addDocsSlice(AiDocsSlice data) {
        data.setCreateTime(new Date());
        data.setWordNum(data.getContent().length());
        data.setStatus(true);
        aiDocsSliceMapper.insert(data);
    }

    @Override
    public void removeSlicesOfDoc(String docsId) {
        LambdaQueryWrapper<AiDocsSlice> deleteWrapper = Wrappers.<AiDocsSlice>lambdaQuery()
            .eq(AiDocsSlice::getDocsId, docsId);
        int count = aiDocsSliceMapper.delete(deleteWrapper);
        log.debug("remove all slices of doc: [{}], count: [{}]", docsId, count);
    }
}
