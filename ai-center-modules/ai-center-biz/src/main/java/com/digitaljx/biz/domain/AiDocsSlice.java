package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 文档切片对象 ai_docs_slice
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_docs_slice")
@Accessors(chain = true)
public class AiDocsSlice extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 向量库的ID
     */
    private String vectorId;

    /**
     * 文档ID
     */
    private Long docsId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 文档名称
     */
    private String name;

    /**
     * 切片内容
     */
    private String content;

    /**
     * 字符数
     */
    private Integer wordNum;

    /**
     * 状态
     */
    private Boolean status = false;


}
