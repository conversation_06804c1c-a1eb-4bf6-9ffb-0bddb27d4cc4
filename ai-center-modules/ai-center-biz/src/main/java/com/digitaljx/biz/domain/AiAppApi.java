package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 应用渠道对象 ai_app_api
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_app_api")
public class AiAppApi extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 应用渠道
     */
    private String channel;

    /**
     * Key
     */
    private String apiKey;


}
