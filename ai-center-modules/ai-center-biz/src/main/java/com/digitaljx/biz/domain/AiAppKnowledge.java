package com.digitaljx.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025/3/1
 */
@Data
@TableName("ai_app_knowledge")
public class AiAppKnowledge {

    /**
     * 应用ID
     */
    @TableId(type = IdType.INPUT)
    private Long appId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;
}
