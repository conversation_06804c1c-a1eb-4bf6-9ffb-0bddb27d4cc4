package com.digitaljx.biz.service.impl;

import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiDocsBo;
import com.digitaljx.biz.domain.vo.AiDocsVo;
import com.digitaljx.biz.domain.AiDocs;
import com.digitaljx.biz.mapper.AiDocsMapper;
import com.digitaljx.biz.service.IAiDocsService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文档Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@RequiredArgsConstructor
@Service
public class AiDocsServiceImpl implements IAiDocsService {

    private final AiDocsMapper baseMapper;

    /**
     * 查询文档
     *
     * @param id 主键
     * @return 文档
     */
    @Override
    public AiDocsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询文档列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 文档分页列表
     */
    @Override
    public TableDataInfo<AiDocsVo> queryPageList(AiDocsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiDocs> lqw = buildQueryWrapper(bo);
        Page<AiDocsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的文档列表
     *
     * @param bo 查询条件
     * @return 文档列表
     */
    @Override
    public List<AiDocsVo> queryList(AiDocsBo bo) {
        LambdaQueryWrapper<AiDocs> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiDocs> buildQueryWrapper(AiDocsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiDocs> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiDocs::getId);
        lqw.eq(bo.getKnowledgeId() != null, AiDocs::getKnowledgeId, bo.getKnowledgeId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), AiDocs::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AiDocs::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), AiDocs::getUrl, bo.getUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getOrigin()), AiDocs::getOrigin, bo.getOrigin());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AiDocs::getContent, bo.getContent());
        lqw.eq(bo.getFileSize() != null, AiDocs::getFileSize, bo.getFileSize());
        lqw.eq(bo.getSliceNum() != null, AiDocs::getSliceNum, bo.getSliceNum());
        lqw.eq(bo.getSliceStatus() != null, AiDocs::getSliceStatus, bo.getSliceStatus());
        return lqw;
    }

    /**
     * 新增文档
     *
     * @param bo 文档
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiDocsBo bo) {
        AiDocs add = MapstructUtils.convert(bo, AiDocs.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改文档
     *
     * @param bo 文档
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiDocsBo bo) {
        AiDocs update = MapstructUtils.convert(bo, AiDocs.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiDocs entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除文档信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
