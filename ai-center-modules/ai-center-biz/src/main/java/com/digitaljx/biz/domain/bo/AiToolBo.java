package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiTool;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 工具业务对象 ai_tool
 *
 * <AUTHOR> Li
 * @date 2025-03-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiTool.class, reverseConvertGenerate = false)
public class AiToolBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 工具类型: 系统/自定义
     */
    private String toolType;

    /**
     * 工具编码
     */
    @NotNull(message = "工具编码不能为空")
    private String toolCode;

    /**
     * 参数,JSON格式
     */
    private String feature;

    /**
     * 备注
     */
    private String remark;


}
