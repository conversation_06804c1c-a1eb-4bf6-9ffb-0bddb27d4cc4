package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiFlowLog;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 流程日志业务对象 ai_flow_log
 *
 * <AUTHOR> Li
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiFlowLog.class, reverseConvertGenerate = false)
public class AiFlowLogBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 流程ID
     */
    @NotNull(message = "流程ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long flowId;

    /**
     * 类型 0-失败 1-成功 2-未知
     */
    private Integer type;

    /**
     * 传入参数
     */
    private String inputParams;

    /**
     * 结果
     */
    private String result;

    /**
     * 节点运行信息
     */
    private String nodeInfo;


}
