package com.digitaljx.biz.service.impl;

import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiCardBo;
import com.digitaljx.biz.domain.vo.AiCardVo;
import com.digitaljx.biz.domain.AiCard;
import com.digitaljx.biz.mapper.AiCardMapper;
import com.digitaljx.biz.service.IAiCardService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 卡片Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-02-26
 */
@RequiredArgsConstructor
@Service
public class AiCardServiceImpl implements IAiCardService {

    private final AiCardMapper baseMapper;

    /**
     * 查询卡片
     *
     * @param id 主键
     * @return 卡片
     */
    @Override
    public AiCardVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询卡片列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 卡片分页列表
     */
    @Override
    public TableDataInfo<AiCardVo> queryPageList(AiCardBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiCard> lqw = buildQueryWrapper(bo);
        Page<AiCardVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的卡片列表
     *
     * @param bo 查询条件
     * @return 卡片列表
     */
    @Override
    public List<AiCardVo> queryList(AiCardBo bo) {
        LambdaQueryWrapper<AiCard> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiCard> buildQueryWrapper(AiCardBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiCard> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiCard::getId);
        lqw.like(StringUtils.isNotBlank(bo.getCardName()), AiCard::getCardName, bo.getCardName());
        lqw.eq(StringUtils.isNotBlank(bo.getCardCode()), AiCard::getCardCode, bo.getCardCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCardType()), AiCard::getCardType, bo.getCardType());
        lqw.eq(StringUtils.isNotBlank(bo.getCardInfo()), AiCard::getCardInfo, bo.getCardInfo());
        lqw.eq(StringUtils.isNotBlank(bo.getEntryType()), AiCard::getEntryType, bo.getEntryType());
        lqw.eq(StringUtils.isNotBlank(bo.getCardStatus()), AiCard::getCardStatus, bo.getCardStatus());
        return lqw;
    }

    /**
     * 新增卡片
     *
     * @param bo 卡片
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiCardBo bo) {
        AiCard add = MapstructUtils.convert(bo, AiCard.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改卡片
     *
     * @param bo 卡片
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiCardBo bo) {
        AiCard update = MapstructUtils.convert(bo, AiCard.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiCard entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除卡片信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
