package com.digitaljx.biz.service.impl;

import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiDocsSliceBo;
import com.digitaljx.biz.domain.vo.AiDocsSliceVo;
import com.digitaljx.biz.domain.AiDocsSlice;
import com.digitaljx.biz.mapper.AiDocsSliceMapper;
import com.digitaljx.biz.service.IAiDocsSliceService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文档切片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@RequiredArgsConstructor
@Service
public class AiDocsSliceServiceImpl implements IAiDocsSliceService {

    private final AiDocsSliceMapper baseMapper;

    /**
     * 查询文档切片
     *
     * @param id 主键
     * @return 文档切片
     */
    @Override
    public AiDocsSliceVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询文档切片列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 文档切片分页列表
     */
    @Override
    public TableDataInfo<AiDocsSliceVo> queryPageList(AiDocsSliceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiDocsSlice> lqw = buildQueryWrapper(bo);
        Page<AiDocsSliceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的文档切片列表
     *
     * @param bo 查询条件
     * @return 文档切片列表
     */
    @Override
    public List<AiDocsSliceVo> queryList(AiDocsSliceBo bo) {
        LambdaQueryWrapper<AiDocsSlice> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiDocsSlice> buildQueryWrapper(AiDocsSliceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiDocsSlice> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(AiDocsSlice::getUpdateTime);
        lqw.eq(StringUtils.isNotBlank(bo.getVectorId()), AiDocsSlice::getVectorId, bo.getVectorId());
        lqw.eq(bo.getDocsId() != null, AiDocsSlice::getDocsId, bo.getDocsId());
        lqw.eq(bo.getKnowledgeId() != null, AiDocsSlice::getKnowledgeId, bo.getKnowledgeId());
        lqw.like(StringUtils.isNotBlank(bo.getName()), AiDocsSlice::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AiDocsSlice::getContent, bo.getContent());
        lqw.eq(bo.getWordNum() != null, AiDocsSlice::getWordNum, bo.getWordNum());
        lqw.eq(bo.getStatus() != null, AiDocsSlice::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增文档切片
     *
     * @param bo 文档切片
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiDocsSliceBo bo) {
        AiDocsSlice add = MapstructUtils.convert(bo, AiDocsSlice.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改文档切片
     *
     * @param bo 文档切片
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiDocsSliceBo bo) {
        AiDocsSlice update = MapstructUtils.convert(bo, AiDocsSlice.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiDocsSlice entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除文档切片信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
