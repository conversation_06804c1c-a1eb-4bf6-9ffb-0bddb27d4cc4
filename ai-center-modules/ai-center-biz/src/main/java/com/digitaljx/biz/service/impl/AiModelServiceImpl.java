package com.digitaljx.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.digitaljx.common.ai.enums.ModelTypeEnum;
import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiModelBo;
import com.digitaljx.biz.domain.vo.AiModelVo;
import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.biz.mapper.AiModelMapper;
import com.digitaljx.biz.service.IAiModelService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 模型Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@RequiredArgsConstructor
@Service
public class AiModelServiceImpl implements IAiModelService {

    private final AiModelMapper baseMapper;

    /**
     * 查询模型
     *
     * @param id 主键
     * @return 模型
     */
    @Override
    public AiModelVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询模型列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模型分页列表
     */
    @Override
    public TableDataInfo<AiModelVo> queryPageList(AiModelBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiModel> lqw = buildQueryWrapper(bo);
        Page<AiModelVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的模型列表
     *
     * @param bo 查询条件
     * @return 模型列表
     */
    @Override
    public List<AiModelVo> queryList(AiModelBo bo) {
        LambdaQueryWrapper<AiModel> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiModel> buildQueryWrapper(AiModelBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiModel> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiModel::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getType()), AiModel::getType, bo.getType());
        lqw.like(StringUtils.isNotBlank(bo.getModelName()), AiModel::getModelName, bo.getModelName());
        lqw.eq(StringUtils.isNotBlank(bo.getProvider()), AiModel::getProvider, bo.getProvider());
        lqw.eq(StringUtils.isNotBlank(bo.getAlias()), AiModel::getAlias, bo.getAlias());
        lqw.eq(bo.getResponseLimit() != null, AiModel::getResponseLimit, bo.getResponseLimit());
        lqw.eq(bo.getTemperature() != null, AiModel::getTemperature, bo.getTemperature());
        lqw.eq(bo.getTopP() != null, AiModel::getTopP, bo.getTopP());
        lqw.eq(StringUtils.isNotBlank(bo.getApiKey()), AiModel::getApiKey, bo.getApiKey());
        lqw.eq(StringUtils.isNotBlank(bo.getBaseUrl()), AiModel::getBaseUrl, bo.getBaseUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getSecretKey()), AiModel::getSecretKey, bo.getSecretKey());
        lqw.eq(StringUtils.isNotBlank(bo.getEndpoint()), AiModel::getEndpoint, bo.getEndpoint());
        lqw.like(StringUtils.isNotBlank(bo.getAzureDeploymentName()), AiModel::getAzureDeploymentName, bo.getAzureDeploymentName());
        lqw.eq(StringUtils.isNotBlank(bo.getGeminiProject()), AiModel::getGeminiProject, bo.getGeminiProject());
        lqw.eq(StringUtils.isNotBlank(bo.getGeminiLocation()), AiModel::getGeminiLocation, bo.getGeminiLocation());
        lqw.eq(StringUtils.isNotBlank(bo.getImageSize()), AiModel::getImageSize, bo.getImageSize());
        lqw.eq(StringUtils.isNotBlank(bo.getImageQuality()), AiModel::getImageQuality, bo.getImageQuality());
        lqw.eq(StringUtils.isNotBlank(bo.getImageStyle()), AiModel::getImageStyle, bo.getImageStyle());
        lqw.eq(bo.getDimension() != null, AiModel::getDimension, bo.getDimension());
        return lqw;
    }

    /**
     * 新增模型
     *
     * @param bo 模型
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiModelBo bo) {
        AiModel add = MapstructUtils.convert(bo, AiModel.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改模型
     *
     * @param bo 模型
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiModelBo bo) {
        AiModel update = MapstructUtils.convert(bo, AiModel.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiModel entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除模型信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<AiModelVo> getImageModels() {
        List<AiModelVo> list = baseMapper.selectVoList(Wrappers.<AiModel>lambdaQuery()
            .eq(AiModel::getType, ModelTypeEnum.TEXT_IMAGE.name()));
        list.forEach(this::hide);
        return list;
    }

    @Override
    public List<AiModelVo> getEmbeddingModels() {
        List<AiModelVo> list = baseMapper.selectVoList(Wrappers.<AiModel>lambdaQuery()
            .eq(AiModel::getType, ModelTypeEnum.EMBEDDING.name()));
        list.forEach(this::hide);
        return list;
    }

    private void hide(AiModelVo model) {
        if (model == null || StrUtil.isBlank(model.getApiKey())) {
            return;
        }
        String key = StrUtil.hide(model.getApiKey(), 3, model.getApiKey().length() - 4);
        model.setApiKey(key);

        if (StrUtil.isBlank(model.getSecretKey())) {
            return;
        }
        String sec = StrUtil.hide(model.getSecretKey(), 3, model.getSecretKey().length() - 4);
        model.setSecretKey(sec);
    }

}
