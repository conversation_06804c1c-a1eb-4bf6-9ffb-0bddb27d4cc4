package com.digitaljx.biz.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.digitaljx.biz.domain.AiDocs;
import com.digitaljx.biz.domain.AiEmbedStore;
import com.digitaljx.biz.domain.AiKnowledge;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 知识库视图对象 ai_knowledge
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiKnowledge.class)
public class AiKnowledgeVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 向量数据库ID
     */
    @ExcelProperty(value = "向量数据库ID")
    private Long embedStoreId;

    /**
     * 向量模型ID
     */
    @ExcelProperty(value = "向量模型ID")
    private Long embedModelId;

    /**
     * 知识库名称
     */
    @ExcelProperty(value = "知识库名称")
    private String name;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String des;

    /**
     * 封面
     */
    @ExcelProperty(value = "封面")
    private String coverImg;

    private AiEmbedStoreVo embedStore;

    private AiModelVo embedModel;

    @TableField(exist = false)
    private Integer docsNum = 0;
    @TableField(exist = false)
    private Long totalSize = 0L;
    @TableField(exist = false)
    private List<AiDocs> docs = new ArrayList<>();

    /**
     * 排序模型ID
     */
    private Long scoringModelId;

}
