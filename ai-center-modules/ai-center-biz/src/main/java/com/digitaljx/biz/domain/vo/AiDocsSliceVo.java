package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiDocsSlice;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 文档切片视图对象 ai_docs_slice
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiDocsSlice.class)
public class AiDocsSliceVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 向量库的ID
     */
    @ExcelProperty(value = "向量库的ID")
    private String vectorId;

    /**
     * 文档ID
     */
    @ExcelProperty(value = "文档ID")
    private Long docsId;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long knowledgeId;

    /**
     * 文档名称
     */
    @ExcelProperty(value = "文档名称")
    private String name;

    /**
     * 切片内容
     */
    @ExcelProperty(value = "切片内容")
    private String content;

    /**
     * 字符数
     */
    @ExcelProperty(value = "字符数")
    private Integer wordNum;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private Boolean status;

    /**
     * 创建时间
     */
    private Date createTime;


}
