package com.digitaljx.biz.service;

import com.digitaljx.biz.domain.AiAppKnowledge;
import com.digitaljx.biz.domain.vo.AiAppVo;
import com.digitaljx.biz.domain.bo.AiAppBo;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 应用Service接口
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
public interface IAiAppService {

    /**
     * 查询应用
     *
     * @param id 主键
     * @return 应用
     */
    AiAppVo queryById(Long id);

    /**
     * 查询应用
     * @param id
     * @return
     */
    AiAppVo getWithKnowledgeIds(Long id);

    /**
     * 分页查询应用列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应用分页列表
     */
    TableDataInfo<AiAppVo> queryPageList(AiAppBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的应用列表
     *
     * @param bo 查询条件
     * @return 应用列表
     */
    List<AiAppVo> queryList(AiAppBo bo);

    /**
     * 新增应用
     *
     * @param bo 应用
     * @return 是否新增成功
     */
    Boolean insertByBo(AiAppBo bo);

    /**
     * 修改应用
     *
     * @param bo 应用
     * @return 是否修改成功
     */
    Boolean updateByBo(AiAppBo bo);

    /**
     * 校验并批量删除应用信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 发布
     * @param flowId
     * @return
     */
    Boolean release(Long flowId);

    Boolean addKnowledge(AiAppKnowledge aiAppKnowledge);

    Boolean removeKnowledge(AiAppKnowledge aiAppKnowledge);
}
