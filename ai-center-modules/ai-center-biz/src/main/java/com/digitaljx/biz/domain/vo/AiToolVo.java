package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiTool;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 工具视图对象 ai_tool
 *
 * <AUTHOR> Li
 * @date 2025-03-20
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiTool.class)
public class AiToolVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    private Long userId;

    /**
     * 工具类型: 系统/自定义
     */
    @ExcelProperty(value = "工具类型: 系统/自定义")
    private String toolType;

    /**
     * 工具编码
     */
    @ExcelProperty(value = "工具编码")
    private String toolCode;

    /**
     * 参数,JSON格式
     */
    @ExcelProperty(value = "参数,JSON格式")
    private String feature;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 工具名称
     */
    private String toolName;


}
