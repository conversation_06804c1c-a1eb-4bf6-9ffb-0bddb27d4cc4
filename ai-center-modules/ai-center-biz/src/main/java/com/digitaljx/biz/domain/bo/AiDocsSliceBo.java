package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiDocsSlice;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 文档切片业务对象 ai_docs_slice
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiDocsSlice.class, reverseConvertGenerate = false)
public class AiDocsSliceBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 向量库的ID
     */
    @NotNull(message = "向量库的ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private String vectorId;

    /**
     * 文档ID
     */
    @NotNull(message = "文档ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long docsId;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long knowledgeId;

    /**
     * 文档名称
     */
    private String name;

    /**
     * 切片内容
     */
    private String content;

    /**
     * 字符数
     */
    private Integer wordNum;

    /**
     * 状态
     */
    private Boolean status;


}
