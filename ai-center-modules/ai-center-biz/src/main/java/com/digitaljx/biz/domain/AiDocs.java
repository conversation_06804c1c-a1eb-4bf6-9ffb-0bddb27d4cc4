package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;

/**
 * 文档对象 ai_docs
 *
 * <AUTHOR>
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_docs")
@Accessors(chain = true)
public class AiDocs extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     *
     */
    private String url;

    /**
     * 来源
     */
    private String origin;

    /**
     * 内容或链接
     */
    private String content;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 切片数量
     */
    private Integer sliceNum;

    /**
     * 切片状态
     */
    private Boolean sliceStatus;

}
