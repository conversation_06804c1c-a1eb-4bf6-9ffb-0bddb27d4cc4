package com.digitaljx.biz.service.impl;

import com.digitaljx.common.core.constant.CacheNames;
import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.ServletUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiAppApiBo;
import com.digitaljx.biz.domain.vo.AiAppApiVo;
import com.digitaljx.biz.domain.AiAppApi;
import com.digitaljx.biz.mapper.AiAppApiMapper;
import com.digitaljx.biz.service.IAiAppApiService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

import static com.digitaljx.common.core.constant.Constants.CHANNEL_API;

/**
 * 应用渠道Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@RequiredArgsConstructor
@Service
public class AiAppApiServiceImpl implements IAiAppApiService {

    private final AiAppApiMapper baseMapper;

    /**
     * 查询应用渠道
     *
     * @param id 主键
     * @return 应用渠道
     */
    @Override
    public AiAppApiVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询应用渠道列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 应用渠道分页列表
     */
    @Override
    public TableDataInfo<AiAppApiVo> queryPageList(AiAppApiBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiAppApi> lqw = buildQueryWrapper(bo);
        Page<AiAppApiVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的应用渠道列表
     *
     * @param bo 查询条件
     * @return 应用渠道列表
     */
    @Override
    public List<AiAppApiVo> queryList(AiAppApiBo bo) {
        LambdaQueryWrapper<AiAppApi> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiAppApi> buildQueryWrapper(AiAppApiBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiAppApi> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiAppApi::getId);
        lqw.eq(bo.getAppId() != null, AiAppApi::getAppId, bo.getAppId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannel()), AiAppApi::getChannel, bo.getChannel());
        lqw.eq(StringUtils.isNotBlank(bo.getApiKey()), AiAppApi::getApiKey, bo.getApiKey());
        return lqw;
    }

    /**
     * 新增应用渠道
     *
     * @param bo 应用渠道
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiAppApiBo bo) {
        AiAppApi add = MapstructUtils.convert(bo, AiAppApi.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改应用渠道
     *
     * @param bo 应用渠道
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiAppApiBo bo) {
        AiAppApi update = MapstructUtils.convert(bo, AiAppApi.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiAppApi entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除应用渠道信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @CacheEvict(cacheNames = CacheNames.AI_APP_API, allEntries = true)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean isExpired(String channel) {
        String token = ServletUtils.getAuthorizationToken();
        if (CHANNEL_API.equals(channel)) {
            AiAppApiVo data = getByApiKey(token);
            return data == null;
        }
        return true;
    }

    @Override
    public AiAppApiVo getByToken() {
        String token = ServletUtils.getAuthorizationToken();
        return getByApiKey(token);
    }

    @Override
    @Cacheable(cacheNames = CacheNames.AI_APP_API, key = "#apiKey")
    public AiAppApiVo getByApiKey(String apiKey) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<AiAppApi>()
            .eq(AiAppApi::getApiKey, apiKey));
    }
}
