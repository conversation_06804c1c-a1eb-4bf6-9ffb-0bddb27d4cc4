package com.digitaljx.biz.domain.vo;

import com.digitaljx.biz.domain.AiDocs;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.digitaljx.common.excel.annotation.ExcelDictFormat;
import com.digitaljx.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 文档视图对象 ai_docs
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AiDocs.class)
public class AiDocsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 知识库ID
     */
    @ExcelProperty(value = "知识库ID")
    private Long knowledgeId;

    /**
     * 名称
     */
    @ExcelProperty(value = "名称")
    private String name;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String url;

    /**
     * 来源
     */
    @ExcelProperty(value = "来源")
    private String origin;

    /**
     * 内容或链接
     */
    @ExcelProperty(value = "内容或链接")
    private String content;

    /**
     * 文件大小
     */
    @ExcelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 切片数量
     */
    @ExcelProperty(value = "切片数量")
    private Integer sliceNum;

    /**
     * 切片状态
     */
    @ExcelProperty(value = "切片状态")
    private Boolean sliceStatus;


}
