package com.digitaljx.biz.domain;

import com.anwen.mongo.annotation.ID;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import static com.anwen.mongo.enums.IdTypeEnum.ASSIGN_ID;

/**
 * 消息对象 ai_message
 *
 * <AUTHOR>
 * @date 2025-02-18
 */
@Data
public class AiMessage implements Serializable {

    /**
     * 主键
     */
    @ID(type = ASSIGN_ID)
    private Long id;

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 会话ID
     */
    private Long conversationId;

    /**
     * 消息的ID
     */
    private String chatId;

    /**
     * 用户名
     */
    private String username;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 角色，user和assistant
     */
    private String role;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 消息内容
     */
    private String content;

    /**
     *
     */
    private Integer tokens;

    /**
     *
     */
    private Integer promptTokens;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
