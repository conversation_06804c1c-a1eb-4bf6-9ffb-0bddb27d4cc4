package com.digitaljx.biz.service.impl;

import com.digitaljx.common.core.utils.MapstructUtils;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.digitaljx.biz.domain.bo.AiToolBo;
import com.digitaljx.biz.domain.vo.AiToolVo;
import com.digitaljx.biz.domain.AiTool;
import com.digitaljx.biz.mapper.AiToolMapper;
import com.digitaljx.biz.service.IAiToolService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 工具Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-03-20
 */
@RequiredArgsConstructor
@Service
public class AiToolServiceImpl implements IAiToolService {

    private final AiToolMapper baseMapper;

    /**
     * 查询工具
     *
     * @param id 主键
     * @return 工具
     */
    @Override
    public AiToolVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询工具列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工具分页列表
     */
    @Override
    public TableDataInfo<AiToolVo> queryPageList(AiToolBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AiTool> lqw = buildQueryWrapper(bo);
        Page<AiToolVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工具列表
     *
     * @param bo 查询条件
     * @return 工具列表
     */
    @Override
    public List<AiToolVo> queryList(AiToolBo bo) {
        LambdaQueryWrapper<AiTool> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AiTool> buildQueryWrapper(AiToolBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AiTool> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(AiTool::getId);
        lqw.eq(bo.getUserId() != null, AiTool::getUserId, bo.getUserId());
        lqw.eq(StringUtils.isNotBlank(bo.getToolType()), AiTool::getToolType, bo.getToolType());
        lqw.eq(StringUtils.isNotBlank(bo.getToolCode()), AiTool::getToolCode, bo.getToolCode());
        lqw.eq(StringUtils.isNotBlank(bo.getFeature()), AiTool::getParams, bo.getFeature());
        return lqw;
    }

    /**
     * 新增工具
     *
     * @param bo 工具
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AiToolBo bo) {
        AiTool add = MapstructUtils.convert(bo, AiTool.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改工具
     *
     * @param bo 工具
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AiToolBo bo) {
        AiTool update = MapstructUtils.convert(bo, AiTool.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AiTool entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除工具信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
