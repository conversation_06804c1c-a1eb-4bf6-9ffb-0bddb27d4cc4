package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiCard;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 卡片业务对象 ai_card
 *
 * <AUTHOR> Li
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiCard.class, reverseConvertGenerate = false)
public class AiCardBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 卡片名称
     */
    @NotBlank(message = "卡片名称不能为空", groups = { AddGroup.class })
    private String cardName;

    /**
     * 卡片编码
     */
    @NotBlank(message = "卡片编码不能为空", groups = { AddGroup.class})
    private String cardCode;

    /**
     * 卡片类型
     */
    private String cardType;

    /**
     * 卡片信息
     */
    private String cardInfo;

    /**
     * 进入方式:1-自动进入;2-手动进入
     */
    private String entryType;

    /**
     * 卡片状态
     */
    private String cardStatus;

}
