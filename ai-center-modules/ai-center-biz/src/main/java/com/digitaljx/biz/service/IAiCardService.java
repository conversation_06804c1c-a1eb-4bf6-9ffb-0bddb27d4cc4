package com.digitaljx.biz.service;

import com.digitaljx.biz.domain.vo.AiCardVo;
import com.digitaljx.biz.domain.bo.AiCardBo;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 卡片Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface IAiCardService {

    /**
     * 查询卡片
     *
     * @param id 主键
     * @return 卡片
     */
    AiCardVo queryById(Long id);

    /**
     * 分页查询卡片列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 卡片分页列表
     */
    TableDataInfo<AiCardVo> queryPageList(AiCardBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的卡片列表
     *
     * @param bo 查询条件
     * @return 卡片列表
     */
    List<AiCardVo> queryList(AiCardBo bo);

    /**
     * 新增卡片
     *
     * @param bo 卡片
     * @return 是否新增成功
     */
    Boolean insertByBo(AiCardBo bo);

    /**
     * 修改卡片
     *
     * @param bo 卡片
     * @return 是否修改成功
     */
    Boolean updateByBo(AiCardBo bo);

    /**
     * 校验并批量删除卡片信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
