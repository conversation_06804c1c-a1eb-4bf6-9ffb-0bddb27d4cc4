package com.digitaljx.biz.domain;

import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 卡片对象 ai_card
 *
 * <AUTHOR> Li
 * @date 2025-02-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai_card")
public class AiCard extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 卡片名称
     */
    private String cardName;

    /**
     * 卡片编码
     */
    private String cardCode;

    /**
     * 卡片类型
     */
    private String cardType;

    /**
     * 卡片信息
     */
    private String cardInfo;

    /**
     * 进入方式:1-自动进入;2-手动进入
     */
    private String entryType;

    /**
     * 卡片状态
     */
    private String cardStatus;


}
