package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiKnowledge;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 知识库业务对象 ai_knowledge
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiKnowledge.class, reverseConvertGenerate = false)
public class AiKnowledgeBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 向量数据库ID
     */
    private Long embedStoreId;

    /**
     * 向量模型ID
     */
    private Long embedModelId;

    /**
     * 知识库名称
     */
    private String name;

    /**
     * 描述
     */
    private String des;

    /**
     * 封面
     */
    private String coverImg;

    /**
     * 排序模型ID
     */
    private Long scoringModelId;

}
