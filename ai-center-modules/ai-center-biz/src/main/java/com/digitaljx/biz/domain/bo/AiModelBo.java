package com.digitaljx.biz.domain.bo;

import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.common.mybatis.core.domain.BaseEntity;
import com.digitaljx.common.core.validate.AddGroup;
import com.digitaljx.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 模型业务对象 ai_model
 *
 * <AUTHOR> Li
 * @date 2025-02-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiModel.class, reverseConvertGenerate = false)
public class AiModelBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 类型: chat、embedding、image
     */
    @NotNull(message = "类型不可为空", groups = { AddGroup.class })
    private String type;

    /**
     * 模型名称
     */
    @NotNull(message = "模型名称不可为空")
    private String modelName;

    /**
     * 供应商
     */
    @NotNull(message = "供应商不可为空")
    private String provider;

    /**
     * 别名
     */
    private String alias;

    /**
     * 响应长度
     */
    private Integer responseLimit;

    /**
     * 温度
     */
    private Double temperature;

    /**
     *
     */
    private Double topP;

    /**
     *
     */
    private String apiKey;

    /**
     *
     */
    private String baseUrl;

    /**
     *
     */
    private String secretKey;

    /**
     *
     */
    private String endpoint;

    /**
     * azure模型参数
     */
    private String azureDeploymentName;

    /**
     * gemini模型参数
     */
    private String geminiProject;

    /**
     * gemini模型参数
     */
    private String geminiLocation;

    /**
     * 图片大小
     */
    private String imageSize;

    /**
     * 图片质量
     */
    private String imageQuality;

    /**
     * 图片风格
     */
    private String imageStyle;

    /**
     * 向量维数
     */
    private Integer dimension;


}
