<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digitaljx.biz.mapper.AiAppKnowledgeMapper">

    <select id="selectKnowledgeIdsByAppId" resultType="Long">
        select ak.id from ai_knowledge ak
        inner join ai_app_knowledge aak
            on ak.id = aak.knowledge_id and aak.app_id = #{appId}
    </select>

</mapper>
