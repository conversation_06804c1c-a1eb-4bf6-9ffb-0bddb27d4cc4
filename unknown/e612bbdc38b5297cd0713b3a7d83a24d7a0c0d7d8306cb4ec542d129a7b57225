package com.digitaljx.biz.service;

import com.digitaljx.biz.domain.vo.AiToolVo;
import com.digitaljx.biz.domain.bo.AiToolBo;
import com.digitaljx.common.mybatis.core.page.TableDataInfo;
import com.digitaljx.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 工具Service接口
 *
 * <AUTHOR>
 * @date 2025-03-20
 */
public interface IAiToolService {

    /**
     * 查询工具
     *
     * @param id 主键
     * @return 工具
     */
    AiToolVo queryById(Long id);

    /**
     * 分页查询工具列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工具分页列表
     */
    TableDataInfo<AiToolVo> queryPageList(AiToolBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工具列表
     *
     * @param bo 查询条件
     * @return 工具列表
     */
    List<AiToolVo> queryList(AiToolBo bo);

    /**
     * 新增工具
     *
     * @param bo 工具
     * @return 是否新增成功
     */
    Boolean insertByBo(AiToolBo bo);

    /**
     * 修改工具
     *
     * @param bo 工具
     * @return 是否修改成功
     */
    Boolean updateByBo(AiToolBo bo);

    /**
     * 校验并批量删除工具信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
