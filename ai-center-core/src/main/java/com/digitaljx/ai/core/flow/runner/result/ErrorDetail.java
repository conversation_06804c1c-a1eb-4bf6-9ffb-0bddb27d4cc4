package com.digitaljx.ai.core.flow.runner.result;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Setter
@Getter
public class ErrorDetail {
    /**
     * 异常出现的节点
     */
    private String nodeId;

    /**
     * 异常信息
     */
    private String message;

    /**
     * 原始 Error
     */
    private Throwable error;

    /**
     * options配置信息
     */
    private Map<String, Object> options;

    public ErrorDetail() {
    }

    public ErrorDetail(String message) {
        this.message = message;
    }

    public ErrorDetail(String nodeId, String message, Throwable error,
        Map<String, Object> options) {
        this.nodeId = nodeId;
        this.message = message;
        this.error = error;
        this.options = options;
    }
}
