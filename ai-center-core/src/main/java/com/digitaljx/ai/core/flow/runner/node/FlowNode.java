package com.digitaljx.ai.core.flow.runner.node;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 节点
 */
@Getter
@Setter
public abstract class FlowNode {
    /** 节点id */
    private String id;
    /** 流程id */
    private Long flowId;
    /** 节点label */
    private String label;
    /** 节点类型 */
    private String type;
    /** 节点配置 */
    private NodeConfig config;
    /** 输入参数 */
    private Map<String, Object> inputParams;
    /** 输入图片参数 */
    private Map<String, String> imageParams;
    /** 上下文 */
    private FlowContext context;

    /**
     * 调用
     * @param context
     */
    public FlowResult invoke(FlowContext context) {
        this.context = context;
        this.inputParams = this.getInputParams(context);
        return this.run(context);
    }

    /**
     * 获得前缀
     * @returns
     */
    public String getPrefix() {
        return String.format("%s.%s", this.type, this.id);
    }

    /**
     * 获得参数前缀
     * @param param
     * @returns
     */
    public String getParamPrefix(NodeConfig.InputParam param) {
        return String.format("%s.%s", param.getNodeType(), param.getNodeId());
    }
    /**
     * 获得参数前缀
     * @param param
     * @returns
     */
    public String getParamPrefix(NodeConfig.OutputParam param) {
        return String.format("%s.%s", param.getNodeType(), param.getNodeId());
    }

    /**
     * 获取输入参数
     * @param context
     */
    private Map<String, Object> getInputParams(FlowContext context) {
        if (context.isDebugOne() || "start".equals(this.type)) {
            return context.getRequestParams();
        }
        List<NodeConfig.InputParam> inputParams = this.config.getInputParams();
        Map<String, Object> datas = context.getData("output");
        Map<String, Object> params = new HashMap<>();
        Map<String, String> imageParams = new HashMap<>();

        if (inputParams == null || inputParams.isEmpty()) {
            return params;
        }
        for (NodeConfig.InputParam param : inputParams) {
            if (param.getField() != null) {
                if (ObjUtil.equal(param.getType(), "image")) {
                    String imageUrl = StrUtil.toStringOrNull(datas.get(String.format("%s.%s", this.getParamPrefix(param), param.getName())));
                    if (StrUtil.isNotBlank(imageUrl)) {
                        imageParams.put(param.getField(), imageUrl);
                    }
                } else {
                    params.put(param.getField(), datas.get(String.format("%s.%s", this.getParamPrefix(param), param.getName())));
                }
            }
        }
        this.inputParams = params;
        this.imageParams = imageParams;
        return params;
    }

    /**
     * 执行
     *
     * @param context
     */
    public abstract FlowResult run(FlowContext context);

    protected Map<String, Object> mergeMaps(Object... maps) {
        Map<String, Object> mergedMap = new HashMap<>();
        for (Object map : maps) {
            if (map instanceof Map) {
                mergedMap.putAll((Map<String, Object>) map);
            }
        }
        return mergedMap;
    }
}
