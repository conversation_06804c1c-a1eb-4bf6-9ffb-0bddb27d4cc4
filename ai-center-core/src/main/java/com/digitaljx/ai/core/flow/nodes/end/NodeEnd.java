package com.digitaljx.ai.core.flow.nodes.end;

import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.node.NodeConfig.OutputParam;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.flow.runner.result.FlowStream;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 结束节点
 */
public class NodeEnd extends FlowNode {

    /**
     * 执行方法
     *
     * @param context 流程上下文
     * @return 执行结果
     */
    @Override
    public FlowResult run(FlowContext context) {
        Map<String, Object> datas = context.getData("output");
        Map<String, Object> result = new HashMap<>();
        FlowStream stream = null;
        List<OutputParam> outputParams = this.getConfig().getOutputParams();
        for (OutputParam param : outputParams) {
            String key = getParamPrefix(param) + "." + param.getName();
            result.put(param.getField(), datas.get(key));
            // 如果是流对象，则设置流
            if (datas.get(key) instanceof FlowStream) {
                stream = (FlowStream) datas.get(key);
            }
        }
        return new FlowResult(true, result, stream);
    }
}
