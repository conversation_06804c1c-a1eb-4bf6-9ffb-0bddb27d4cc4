package com.digitaljx.ai.core.flow.nodes.variable;

import cn.hutool.core.util.ReflectUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.node.NodeConfig.OutputParam;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.common.core.exception.ServiceException;
import org.codehaus.janino.SimpleCompiler;

import java.util.List;
import java.util.Map;

public class NodeVariable extends FlowNode {


    public FlowResult run(FlowContext context) {
        Map<String, Object> inputParams = this.getInputParams();
        List<OutputParam> outputParams = this.getConfig().getOutputParams();

        // 执行代码进行转换
        Map<String, Object> result = exec(this.getConfig().getOptions().get("code").toString(), inputParams);
        // 输出
        for (OutputParam outputParam : outputParams) {
            String paramField = outputParam.getField();
            context.set(getPrefix() + "." + paramField, result.get(paramField), "output");
        }

        return new FlowResult(true, result);
    }

    private Map<String, Object> exec(String code, Map<String, Object> params) {
        try{
            SimpleCompiler compiler = new SimpleCompiler();
            compiler.cook(code);
            ClassLoader classLoader = compiler.getClassLoader();
            Class<?> dynamicClass = classLoader.loadClass("DynamicClass");

            // 实例化并调用方法
            Object instance = dynamicClass.getDeclaredConstructor().newInstance();
            return ReflectUtil.invoke(instance, "main", params);
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
    }

}
