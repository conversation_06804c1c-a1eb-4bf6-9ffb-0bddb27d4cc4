package com.digitaljx.ai.core.flow.nodes.judge;

import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiPredicate;

public class ConditionMethods {
    private static final Map<String, BiPredicate<String, String>> methods = new HashMap<>();

    static {
        methods.put("include", StringUtils::contains);
        methods.put("exclude", (paramValue, value) -> !StringUtils.contains(paramValue, value));
        methods.put("startWith", StringUtils::startsWith);
        methods.put("endWith", StringUtils::endsWith);
        methods.put("equal", StringUtils::equals);
        methods.put("notEqual", (paramValue, value) -> !StringUtils.equals(paramValue, value));
        methods.put("greaterThan", (paramValue, value) -> Double.parseDouble(paramValue) > Double.parseDouble(value));
        methods.put("greaterThanOrEqual", (paramValue, value) -> Double.parseDouble(paramValue) >= Double.parseDouble(value));
        methods.put("lessThan", (paramValue, value) -> Double.parseDouble(paramValue) < Double.parseDouble(value));
        methods.put("lessThanOrEqual", (paramValue, value) -> Double.parseDouble(paramValue) <= Double.parseDouble(value));
        methods.put("isNull", (paramValue, value) -> paramValue == null);
        methods.put("isNotNull", (paramValue, value) -> paramValue != null);
    }

    public static BiPredicate<String, String> getMethod(String condition) {
        return methods.get(condition);
    }
}
