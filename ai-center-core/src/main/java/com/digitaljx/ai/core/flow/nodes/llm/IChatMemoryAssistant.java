package com.digitaljx.ai.core.flow.nodes.llm;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.service.*;

public interface IChatMemoryAssistant {

    @SystemMessage("{{sm}}")
    TokenStream chatWithSystemStream(@MemoryId String objectId, @V("sm") String systemMessage, @UserMessage String userMessage);

    @SystemMessage("{{sm}}")
    Response<AiMessage> chatWithSystem(@MemoryId String objectId, @V("sm") String systemMessage, @UserMessage String userMessage);

    TokenStream stream(@MemoryId String id, @UserMessage String message);

    String text(@MemoryId String id, @UserMessage String message);
}
