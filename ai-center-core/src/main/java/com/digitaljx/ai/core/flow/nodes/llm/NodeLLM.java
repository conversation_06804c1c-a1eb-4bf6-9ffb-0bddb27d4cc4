package com.digitaljx.ai.core.flow.nodes.llm;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.digitaljx.ai.core.flow.rag.DefaultRetrievalAugmentor;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.context.NodeInfo;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.flow.runner.result.FlowStream;
import com.digitaljx.ai.core.provider.ModelProvider;
import com.digitaljx.ai.core.service.impl.CustomRedisChatMemoryStore;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.data.message.*;
import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.McpTransport;
import dev.langchain4j.mcp.client.transport.http.HttpMcpTransport;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.chat.response.StreamingChatResponseHandler;
import dev.langchain4j.model.input.structured.StructuredPrompt;
import dev.langchain4j.model.input.structured.StructuredPromptProcessor;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.rag.content.aggregator.ContentAggregator;
import dev.langchain4j.rag.query.router.QueryRouter;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.TokenStream;
import dev.langchain4j.service.tool.ToolProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.utils.StringUtils;

import java.io.IOException;
import java.lang.reflect.Array;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

import static com.digitaljx.ai.core.flow.nodes.enums.NodeTypeEnum.know;
import static com.digitaljx.common.ai.constant.FlowConst.UN_KNOW_MESSAGE;

@Slf4j
public class NodeLLM extends FlowNode {

    @Override
    public FlowResult run(FlowContext context) {
        Map<String, Object> options = this.getConfig().getOptions();
        Map<String, Object> model = MapUtil.get(options, "model", new TypeReference<>() {});
        List messages = Optional.ofNullable(options.get("messages"))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
        int history = MapUtil.getInt(options, "history", 0);

        Map<String, Object> params = this.getInputParams();
        List<ChatMessage> list = getChatMessages(messages, context, params, history);

        if(list.isEmpty() || list.stream().noneMatch(o -> ChatMessageType.USER.equals(o.type()))) {
            throw new ServiceException("LLM节点USER消息不能为空");
        }

        if (context.isStream()) {
            FlowStream flowStream = Optional.ofNullable(context.getFlowStream())
                .orElseGet(FlowStream::new);
            handleStreamMode(context, flowStream, model, history, list);
            context.set(this.getPrefix() + ".stream", flowStream, "output");
            return new FlowResult(true, new HashMap<>(), flowStream);
        } else {
            return new FlowResult(true, handleNonStreamMode(context, model, history, list));
        }
    }

    private void handleStreamMode(FlowContext context, FlowStream flowStream, Map<String, Object> model, int history, List<ChatMessage> list) {
        CountDownLatch latch = new CountDownLatch(1);
        try {
            // 多模态，无法使用assistant
            Map<String, String> imageParams = this.getImageParams();
            if (MapUtil.isNotEmpty(imageParams)) {
                Map<String, Object> params = MapUtil.get(model, "params", new TypeReference<>() {});
                Long modelId = MapUtil.getLong(params, "modelId");
                if (modelId == null) {
                    throw new ServiceException("参数错误，请重新保存");
                }
                ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
                StreamingChatLanguageModel streamingModel = modelProvider.stream(modelId);
                streamingModel.chat(list, new StreamingChatResponseHandler() {
                    @Override
                    public void onPartialResponse(String content) {
                        writeSafe(flowStream, content);
                    }
                    @Override
                    public void onCompleteResponse(ChatResponse response) {
                        closeSafe(latch, flowStream, context, response);
                    }
                    @Override
                    public void onError(Throwable throwable) {
                        handleError(latch, flowStream, throwable);
                    }
                });
            } else {
                Map<ChatMessageType, String> map = getMessageTypeListMap(list);
                String prompt = toString(map.get(ChatMessageType.USER));
                TokenStream tokenStream;
                if (history > 0) {
                    IChatMemoryAssistant chatAssistant = getLanguageModel(true, context, model, history);
                    tokenStream = chatAssistant.chatWithSystemStream(context.getObjectId(), toString(map.get(ChatMessageType.SYSTEM)), prompt);
                } else {
                    IChatAssistant chatAssistant = getLanguageModel(true, context, model);
                    tokenStream = chatAssistant.chatWithSystemStream(toString(map.get(ChatMessageType.SYSTEM)), prompt);
                }
                tokenStream.onPartialResponse(content -> writeSafe(flowStream, content))
                    .onCompleteResponse(response -> closeSafe(latch, flowStream, context, response))
                    .onError(throwable -> handleError(latch, flowStream, throwable))
                    .start();
            }
        } catch (ServiceException e) {
            ThreadUtil.execAsync(() -> writeAndCloseSafe(latch, flowStream, UN_KNOW_MESSAGE));
        }
        try {
            latch.await();
        } catch (InterruptedException ignore) {}
    }

    private static Map<ChatMessageType, String> getMessageTypeListMap(List<ChatMessage> list) {
        // 使用 Collectors.toMap 进行收集，并处理可能的键冲突
        return list.stream()
            .collect(Collectors.toMap(
                ChatMessage::type, // 键：消息类型
                ChatMessage::text, // 值：消息内容
                (existing, replacement) -> existing // 处理键冲突：保留原有值
            ));
    }

    private boolean isMemory(String objectId, int history) {
        return StrUtil.isNotBlank(objectId) && history > 0;
    }

    private Map<String, Object> handleNonStreamMode(FlowContext context, Map<String, Object> model, int history, List<ChatMessage> list) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 多模态，无法使用assistant
            Map<String, String> imageParams = this.getImageParams();
            if (MapUtil.isNotEmpty(imageParams)) {
                Map<String, Object> params = MapUtil.get(model, "params", new TypeReference<>() {});
                Long modelId = MapUtil.getLong(params, "modelId");
                if (modelId == null) {
                    throw new ServiceException("参数错误，请重新保存");
                }
                ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
                ChatLanguageModel chatLanguageModel = modelProvider.text(modelId);
                ChatResponse response = chatLanguageModel.chat(list);
                if (ObjUtil.isNotNull(response)) {
                    context.set(this.getPrefix() + ".text", response.aiMessage().text(), "output");
                    context.updateCount("tokenUsage", response.tokenUsage() != null
                        && response.tokenUsage().totalTokenCount() != null ? response.tokenUsage().totalTokenCount() : 0);
                    result.put("text", response.aiMessage().text());
                    return result;
                }
            } else {
                Map<ChatMessageType, String> map = getMessageTypeListMap(list);
                String prompt = toString(map.get(ChatMessageType.USER));
                Response<AiMessage> response;
                if (history > 0) {
                    IChatMemoryAssistant chatAssistant = getLanguageModel(false, context, model, history);
                    response = chatAssistant.chatWithSystem(context.getObjectId(), toString(map.get(ChatMessageType.SYSTEM)), prompt);
                } else {
                    IChatAssistant chatAssistant = getLanguageModel(false, context, model);
                    response = chatAssistant.chatWithSystem(toString(map.get(ChatMessageType.SYSTEM)), prompt);
                }
                if (ObjUtil.isNotNull(response)) {
                    context.set(this.getPrefix() + ".text", response.content().text(), "output");
                    context.updateCount("tokenUsage", response.tokenUsage() != null
                        && response.tokenUsage().totalTokenCount() != null ? response.tokenUsage().totalTokenCount() : 0);                    result.put("text", response.content().text());
                    return result;
                }
            }
        } catch (ServiceException ignored) {}
        context.set(this.getPrefix() + ".text", UN_KNOW_MESSAGE, "output");
        result.put("text", UN_KNOW_MESSAGE);
        return result;
    }

    private void writeSafe(FlowStream flowStream, String content) {
        try {
            flowStream.getPipedOutputStream().write(content.getBytes());
        } catch (IOException e) {
            log.error("Failed to write content: {}", content, e);
        }
    }

    private void closeSafe(CountDownLatch latch, FlowStream flowStream, FlowContext context, ChatResponse response) {
        try {
            flowStream.getPipedOutputStream().close();
        } catch (IOException e) {
            log.error("Failed to close stream", e);
        }
        context.updateCount("tokenUsage", response.tokenUsage() != null
            && response.tokenUsage().totalTokenCount() != null ? response.tokenUsage().totalTokenCount() : 0);
        latch.countDown();
    }

    private void handleError(CountDownLatch latch, FlowStream flowStream, Throwable throwable) {
        try {
            flowStream.getPipedOutputStream().close();
        } catch (IOException e) {
            log.error("Error handling exception: {}", throwable.getMessage(), e);
        }
        latch.countDown();
    }

    private void writeAndCloseSafe(CountDownLatch latch, FlowStream flowStream, String message) {
        try {
            flowStream.getPipedOutputStream().write(message.getBytes());
            flowStream.getPipedOutputStream().close();
        } catch (IOException e) {
            log.error("Failed to write and close stream with message: {}", message, e);
        }
        latch.countDown();
    }

    private String toString(Object arg) {
        if (arg.getClass().isArray()) {
            return arrayToString(arg);
        } else if (arg.getClass().isAnnotationPresent(StructuredPrompt.class)) {
            return StructuredPromptProcessor.toPrompt(arg).text();
        } else {
            return arg.toString();
        }
    }

    private String arrayToString(Object arg) {
        StringBuilder sb = new StringBuilder();
        int length = Array.getLength(arg);
        for (int i = 0; i < length; i++) {
            sb.append(toString(Array.get(arg, i)));
            if (i < length - 1) {
                sb.append(", ");
            }
        }
        return sb.toString();
    }

    Pair<QueryRouter, Boolean> getQueryRouter(FlowContext context, NodeInfo nodeInfo) {
        Map<String, Object> data = context.getData("input");
        Object queryRouterObj = data.get(String.format("%s.%s.queryRouter", nodeInfo.getType(), nodeInfo.getId()));
        Object breakIfSearchMissedObj = data.get(String.format("%s.%s.breakIfSearchMissed", nodeInfo.getType(), nodeInfo.getId()));
        return Pair.of((QueryRouter) queryRouterObj, breakIfSearchMissedObj != null ? (Boolean)breakIfSearchMissedObj : false);
    }

    private ContentAggregator getContentAggregator(FlowContext context, NodeInfo nodeInfo) {
        Map<String, Object> data = context.getData("input");
        return (ContentAggregator) data.get(String.format("%s.%s.contentAggregator", nodeInfo.getType(), nodeInfo.getId()));
    }

    private IChatMemoryAssistant getLanguageModel(boolean stream, FlowContext context, Map<String, Object> model, int historyCnt) {
        Map<String, Object> params = MapUtil.get(model, "params", new TypeReference<>() {});
        Long modelId = MapUtil.getLong(params, "modelId");
        if (modelId == null) {
            throw new ServiceException("参数错误，请重新保存");
        }
        ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
        NodeInfo nodeInfo = getPrevNodeInfo(context);
        AiServices<IChatMemoryAssistant> builder = AiServices.builder(IChatMemoryAssistant.class);

        if (stream) {
            StreamingChatLanguageModel llm = modelProvider.stream(modelId);
            builder.streamingChatLanguageModel(llm);
        } else {
            ChatLanguageModel llm = modelProvider.text(modelId);
            builder.chatLanguageModel(llm);
        }

        String objectId = context.getObjectId();
        builder.chatMemoryProvider(memoryId -> MessageWindowChatMemory.builder()
            .id(objectId)
            .chatMemoryStore(new CustomRedisChatMemoryStore())
            .maxMessages(historyCnt)
            .build());
        if (ObjUtil.isNotNull(nodeInfo) && ObjUtil.equals(nodeInfo.getType(), know.name())) {
            // 前一节点为知识库，进行rag大模型增强
            Pair<QueryRouter, Boolean> pair = getQueryRouter(context, nodeInfo);
            if (ObjUtil.isNotNull(pair.getKey())) {
                builder.retrievalAugmentor(DefaultRetrievalAugmentor.builder()
                    .queryRouter(pair.getKey())
                    .contentAggregator(getContentAggregator(context, nodeInfo))
                    .breakIfSearchMissed(pair.getValue())
                    .build());
            }
        }
        return builder
//            .toolProvider(buildMcp())
            .build();
    }

    private IChatAssistant getLanguageModel(boolean stream, FlowContext context, Map<String, Object> model) {
        Map<String, Object> params = MapUtil.get(model, "params", new TypeReference<>() {});
        Long modelId = MapUtil.getLong(params, "modelId");
        if (modelId == null) {
            throw new ServiceException("参数错误，请重新保存");
        }
        // TODO: temperature
        ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
        NodeInfo nodeInfo = getPrevNodeInfo(context);
        AiServices<IChatAssistant> builder = AiServices.builder(IChatAssistant.class);

        if (stream) {
            StreamingChatLanguageModel llm = modelProvider.stream(modelId);
            builder.streamingChatLanguageModel(llm);
        } else {
            ChatLanguageModel llm = modelProvider.text(modelId);
            builder.chatLanguageModel(llm);
        }

        if (ObjUtil.isNotNull(nodeInfo) && ObjUtil.equals(nodeInfo.getType(), know.name())) {
            // 前一节点为知识库，进行rag大模型增强
            Pair<QueryRouter, Boolean> pair = getQueryRouter(context, nodeInfo);
            if (ObjUtil.isNotNull(pair.getKey())) {
                builder.retrievalAugmentor(DefaultRetrievalAugmentor.builder()
                    .queryRouter(pair.getKey())
                    .contentAggregator(getContentAggregator(context, nodeInfo))
                    .breakIfSearchMissed(pair.getValue())
                    .build());
            }
        }
        return builder
//            .toolProvider(buildMcp())
            .build();
    }

//    private ToolProvider buildMcp() {
//        McpTransport transport = new HttpMcpTransport.Builder()
//            .sseUrl("http://localhost:8980/sse")
//            .logRequests(true)
//            .logResponses(true)
//            .build();
//
//        McpClient mcpClient = new DefaultMcpClient.Builder()
//            .transport(transport)
//            .build();
//
//        return McpToolProvider.builder()
//            .mcpClients(List.of(mcpClient))
//            .build();
//    }

    private NodeInfo getPrevNodeInfo(FlowContext context) {
        String prev = context.getFlowNodeExec().getPrev();
        return context.getFlowGraph().getNodes().stream()
            .filter(o -> ObjUtil.equals(o.getId(), prev)).findFirst().orElse(null);
    }

    public List<ChatMessage> getChatMessages(List<LinkedHashMap<String, Object>> messages, FlowContext context, Map<String, Object> params, int history) {
        List<ChatMessage> // 转换格式
            messageList = messages.stream()
        .filter(item -> ObjectUtil.isNotEmpty(item.get("content")))
        .map(item -> {
            if ("system".equals(item.get("role"))) {
                return new SystemMessage(replace(item.get("content").toString(), params));
            } else if ("user".equals(item.get("role"))) {
                return new UserMessage(replace(item.get("content").toString(), params));
            }
            return new AiMessage(replace(item.get("content").toString(), params));
        })
        .collect(Collectors.toList());

        if (history > 0) {
            String objectId = context.getObjectId();
            if (StringUtils.isBlank(objectId)) {
                throw new ServiceException("需要保存历史信息，请求参数必须包含objectId (请添加objectId字段的入参，标记为会话Id)");
            }
        }
        return messageList;
    }

    private String replace(String content, Map<String, Object> params) {
        if (params == null) {
            return content;
        }
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() == null) {
                continue;
            }
            content = content.replace("{" + entry.getKey() + "}", entry.getValue().toString());
        }
        return content;
    }

}
