package com.digitaljx.ai.core.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.digitaljx.ai.core.flow.nodes.NodeFactory;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.context.FlowGraph;
import com.digitaljx.ai.core.flow.runner.context.NodeInfo;
import com.digitaljx.ai.core.flow.runner.exec.FlowExecutor;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.node.NodeConfig;
import com.digitaljx.ai.core.flow.runner.result.ErrorDetail;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.flow.runner.result.FlowStream;
import com.digitaljx.biz.domain.AiApp;
import com.digitaljx.biz.domain.bo.AiFlowLogBo;
import com.digitaljx.biz.mapper.AiAppMapper;
import com.digitaljx.biz.service.IAiFlowLogService;
import com.digitaljx.common.core.exception.ServiceException;
import com.digitaljx.common.json.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class FlowRunService {

    private final FlowExecutor flowExecutor;

    private final IAiFlowLogService flowLogService;
    private final AiAppMapper aiAppMapper;

    /**
     * 调试
     * @param params 参数
     * @param label 标签
     * @param nodeId 节点ID
     * @param callback 回调
     */
    public FlowResult debug(Map<String, Object> params, String label, String nodeId, boolean stream, FlowStream flowStream, FlowExecutor.FlowCallback callback)
        throws IOException {
        long start = Instant.now().toEpochMilli();
        try {
            // 上下文
            FlowContext context = new FlowContext();
            context.setFlowStream(flowStream);
            // 设置请求参数
            context.setRequestParams(params);
            // 调试的时候非流式调用
            context.setStream(stream);
            // 获得所有节点
            Pair<AiApp, List<FlowNode>> pair = getNodes(label, true);
            var info = pair.getKey();
            var nodes = pair.getValue();
            // 设置流程图
            FlowGraph flowGraph = JsonUtils.parseObject(info.getDraft(), FlowGraph.class);
            context.setFlowGraph(flowGraph);
            FlowResult result = null;
            if (nodeId != null) {
                // 调试单个节点
                var node = nodes.stream().filter(item -> item.getId().equals(nodeId)).findFirst().orElse(null);
                if (node != null) {
                    // 设置调试参数
                    context.setDebugOne(true);
                    result = flowExecutor.one(node, context, callback);
                }
            } else {
                // 调用流程
                result = flowExecutor.serial(nodes, context, callback);

                // 保存日志
                saveLog(result.isSuccess(), label, params, result.getResult(), result.getError(), result.getNodesResult());
            }
            return result;
        } catch (Exception error) {
            log.error("业务错误", error);

            // 保存日志
            saveLog(false, label, params, null, null, null);

            long end = Instant.now().toEpochMilli();
            callback.execute(new FlowExecutor.FlowCallbackResult(
                new FlowResult(new ErrorDetail(error.getMessage())),
                end - start,
                true
            ));
        }
        return null;
    }
    public FlowResult invoke(Map<String, Object> params, String label, boolean stream) {
        return invoke(params, label, stream, null);
    }

    /**
     * 调用
     * @param params 参数
     * @param label 标签
     * @param stream 是否流式调用
     * @returns
     */
    public FlowResult invoke(Map<String, Object> params, String label, boolean stream, FlowStream flowStream) {
        try {
            // 上下文
            FlowContext context = new FlowContext();
            context.setFlowStream(flowStream);
            // 设置开始参数
            context.set("start", params, "input");
            // 设置请求参数
            context.setRequestParams(params);
            // 设置流式调用
            context.setStream(stream);
            // 获得所有节点
            Pair<AiApp, List<FlowNode>> pair = getNodes(label, true);
            var info = pair.getKey();
            var nodes = pair.getValue();

            // 设置流程图
            FlowGraph flowGraph = JsonUtils.parseObject(info.getDraft(), FlowGraph.class);
            context.setFlowGraph(flowGraph);
            // 调用流程
            FlowResult result = flowExecutor.serial(nodes, context, null);

            // 保存日志
            saveLog(result.isSuccess(), label, params, result.getResult(), result.getError(), result.getNodesResult());
            result.setNodesResult(null);
            return result;
        } catch (Exception error) {
            // 保存日志
            saveLog(false, label, params, null, null, null);
        }
        return null;
    }

    /**
     * 保存日志
     * @param success 是否成功 true | false
     * @param label 标签
     * @param inputParams 输入参数
     * @param result 结果
     * @param error 错误信息
     * @param nodeInfo 节点信息
     */
    private void saveLog(boolean success, String label, Map<String, Object> inputParams, Object result, ErrorDetail error, List<Map<String, Object>> nodeInfo) {
        try {
            AiApp app = aiAppMapper.selectOne(new LambdaQueryWrapper<AiApp>()
                .eq(AiApp::getLabel, label));
            if (app != null) {
                AiFlowLogBo flowLog = new AiFlowLogBo();
                flowLog.setFlowId(app.getId());
                flowLog.setType(success ? 1 : 0);
                flowLog.setInputParams(JsonUtils.toJsonString(inputParams));
                flowLog.setResult(JsonUtils.toJsonString(result));
                flowLog.setNodeInfo(JsonUtils.toJsonString(nodeInfo));
                // 保存日志
                flowLogService.insertByBo(flowLog);
            }
        } catch (Exception e) {
            log.error("保存日志错误", e);
        }
    }

    private Pair<AiApp, List<FlowNode>> getNodes(String label, boolean isDraft) {
        AiApp flowInfo = aiAppMapper.selectOne(
            new LambdaQueryWrapper<AiApp>().eq(AiApp::getLabel, label)
                .eq(AiApp::getStatus, 1));
        if (flowInfo == null) {
            throw new ServiceException("流程不存在或被禁用");
        }

        var data = isDraft ? flowInfo.getDraft() : flowInfo.getData();
        List<FlowNode> nodes = new ArrayList<>();

        FlowGraph flowGraph = JsonUtils.parseObject(data, FlowGraph.class);


        // 构建所有节点
        if (flowGraph != null) {
            for (NodeInfo item : flowGraph.getNodes()) {
                FlowNode node = NodeFactory.getNode(item.getType());
                node.setId(item.getId());
                node.setFlowId(flowInfo.getId());
                node.setLabel(item.getLabel());
                node.setType(item.getType());
                node.setConfig(new NodeConfig(
                    item.getData().getInputParams(),
                    item.getData().getOutputParams(),
                    item.getData().getOptions()
                ));
                nodes.add(node);
            }
        }
        return new Pair<>(flowInfo, nodes);
    }
}
