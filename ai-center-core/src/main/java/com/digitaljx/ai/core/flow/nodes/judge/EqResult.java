package com.digitaljx.ai.core.flow.nodes.judge;

import lombok.Getter;
import lombok.Setter;

/**
 * 对比结果
 */
@Setter
@Getter
public class EqResult {
    /** 结果 */
    private boolean result;
    /** 操作符 AND | OR */
    private String operator;

    public EqResult() {
    }

    // 构造函数
    public EqResult(boolean result, String operator) {
        this.result = result;
        this.operator = operator;
    }
}
