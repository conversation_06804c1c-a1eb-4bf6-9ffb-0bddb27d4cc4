package com.digitaljx.ai.core.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.digitaljx.ai.core.provider.EmbeddingProvider;
import com.digitaljx.ai.core.provider.ModelProvider;
import com.digitaljx.biz.domain.AiKnowledge;
import com.digitaljx.biz.mapper.AiKnowledgeMapper;
import com.google.common.collect.Lists;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.scoring.ScoringModel;
import dev.langchain4j.rag.content.aggregator.ContentAggregator;
import dev.langchain4j.rag.content.aggregator.DefaultContentAggregator;
import dev.langchain4j.rag.content.aggregator.ReRankingContentAggregator;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.rag.query.router.DefaultQueryRouter;
import dev.langchain4j.rag.query.router.QueryRouter;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingSearchResult;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.filter.Filter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.digitaljx.common.ai.constant.EmbedConst.KNOWLEDGE;
import static dev.langchain4j.store.embedding.filter.MetadataFilterBuilder.metadataKey;

@Service
@RequiredArgsConstructor
public class KnowRetrieverService {

    private final EmbeddingProvider embeddingProvider;
    private final ModelProvider modelProvider;
    private final AiKnowledgeMapper knowledgeMapper;

    /**
     * 调用检索服务
     *
     * @param knowId 知识库ID
     * @param text   查询文本
     * @param size
     * @return 文档及其得分
     */
    public List<EmbeddingMatch<TextSegment>> invoke(Long knowId, String text, Integer size, Double minScore) {
        EmbeddingModel embeddingModel = embeddingProvider.getEmbeddingModel(knowId);
        EmbeddingStore<TextSegment> embeddingStore = embeddingProvider.getEmbeddingStore(knowId);
        Embedding queryEmbedding = embeddingModel.embed(text).content();
        Filter filter = metadataKey(KNOWLEDGE).isEqualTo(knowId);
        EmbeddingSearchResult<TextSegment> result = embeddingStore.search(EmbeddingSearchRequest
            .builder()
            .queryEmbedding(queryEmbedding)
            .filter(filter)
            .build());
        return result.matches();
    }

    /**
     * 根据知识库ID、返回结果大小和最小分数获取查询路由器
     *
     * @param knowIds 知识库ID，用于指定查询的知识库
     * @param retrieveMaxResults 文档召回最大数量
     * @param retrieveMinScore 文档召回最小分数
     * @return 返回一个配置好的QueryRouter实例，用于执行具体的查询操作
     */
    public QueryRouter getQueryRouter(List<Long> knowIds, Integer retrieveMaxResults, Double retrieveMinScore, boolean breakIfSearchMissed) {
        List<ContentRetriever> list = Lists.newArrayList();
        for (Long knowId : knowIds) {
            // 获取指定知识库的嵌入模型
            EmbeddingModel embeddingModel = embeddingProvider.getEmbeddingModel(knowId);
            // 获取知识库存储基础实例
            // 根据知识库ID获取嵌入存储实例
            EmbeddingStore<TextSegment> embeddingStore = embeddingProvider.getEmbeddingStore(knowId);
            // 创建一个过滤器，用于筛选启用的知识库项
            Filter filter = metadataKey(KNOWLEDGE).isEqualTo(knowId);
            // 构建一个嵌入存储内容检索器，用于从嵌入存储中检索内容
            EmbeddingStoreContentRetriever contentRetriever = EmbeddingStoreContentRetriever.builder()
                    .embeddingStore(embeddingStore)
                    .embeddingModel(embeddingModel)
                    .maxResults(retrieveMaxResults)
                    .minScore(retrieveMinScore)
                    .filter(filter)
                    .build();
            list.add(contentRetriever);
        }

        // 返回一个新的默认查询路由器实例，配置了之前构建的内容检索器
        return new DefaultQueryRouter(list);
    }

    /**
     * 执行搜索
     *
     * @param knowIds 知识库ID列表
     * @param text    查询文本
     * @param size
     * @return 搜索结果
     */
    public List<EmbeddingMatch<TextSegment>> search(List<Long> knowIds, String text, Integer size, Double minScore) {
        List<EmbeddingMatch<TextSegment>> list = Lists.newArrayList();
        for (Long knowId : knowIds) {
            list.addAll(invoke(knowId, text, size, minScore));
        }
        return list;
    }

    /**
     *
     * 获取内容聚合器, 结合评分重排序
     *
     * @param knowIds 知识库ID列表
     * @param minScore 最小分数
     * @return 内容聚合器
     */
    public ContentAggregator getContentAggregator(List<Long> knowIds, Double minScore) {
        if (ObjUtil.isEmpty(knowIds)) {
            return null;
        }

        List<AiKnowledge> knowledges = knowledgeMapper.selectList(new LambdaQueryWrapper<AiKnowledge>()
            .in(AiKnowledge::getId, knowIds)
            .isNotNull(AiKnowledge::getScoringModelId));
        if (CollUtil.isEmpty(knowledges)) {
            return new DefaultContentAggregator();
        }

        Long modelId = knowledges.get(0).getScoringModelId();
        ScoringModel scoringModel = modelProvider.scoring(modelId);
        return ReRankingContentAggregator.builder()
            .scoringModel(scoringModel)
            .minScore(minScore)
            .build();
    }
}
