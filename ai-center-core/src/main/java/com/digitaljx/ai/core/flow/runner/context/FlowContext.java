package com.digitaljx.ai.core.flow.runner.context;

import cn.hutool.core.map.MapUtil;
import com.digitaljx.ai.core.flow.runner.result.FlowStream;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

import static com.digitaljx.common.core.constant.Constants.PARAM_OBJECT_ID;

/**
 * 上下文
 */
public class FlowContext {

    // 存储输入输出数据
    private FlowContextData data = new FlowContextData();

    /**
     * -- SETTER --
     *  设置请求参数
     *
     *
     * -- GETTER --
     *  获得请求参数
     *
     @param params
      * @return
     */
    // 请求参数
    @Getter
    @Setter
    private Map<String, Object> requestParams = new HashMap<>();

    // 调试单个节点
    @Setter
    @Getter
    private boolean debugOne = false;

    // 统计
    @Getter
    private Map<String, Integer> count = new HashMap<>();

    /**
     * -- SETTER --
     *  设置执行信息
     *
     *
     * -- GETTER --
     *  获取执行信息
     *
     @param flowNodeExec
      * @return
     */
    // 执行信息
    @Getter
    @Setter
    private FlowNodeExec flowNodeExec = new FlowNodeExec();

    /**
     * -- GETTER --
     *  获取流程图
     *
     * @return
     */
    // 流程图信息
    @Getter
    @Setter
    private FlowGraph flowGraph;

    // 是否流式调用
    @Setter
    @Getter
    private boolean stream = false;

    @Setter
    @Getter
    private FlowStream flowStream;

    /**
     * 更新统计
     * @param key
     * @param increment
     */
    public void updateCount(String key, int increment) {
        count.put(key, count.getOrDefault(key, 0) + increment);
    }

    /**
     * 设置数据
     * @param key 键
     * @param value 值
     * @param type 类型
     */
    public void set(String key, Object value, String type) {
        if ("input".equals(type)) {
            data.getInput().put(key, value);
        } else if ("output".equals(type)) {
            data.getOutput().put(key, value);
        }
    }

    /**
     * 获取数据
     * @param key
     * @param type
     * @return
     */
    public Object get(String key, String type) {
        return "input".equals(type) ? data.getInput().get(key) : data.getOutput().get(key);
    }

    /**
     * 获取所有输入数据或输出数据
     * @param type
     * @return
     */
    public Map<String, Object> getData(String type) {
        return "input".equals(type) ? data.getInput() : data.getOutput();
    }

    /**
     * 会话ID
     * @return
     */
    public String getObjectId() {
        return MapUtil.getStr(this.getRequestParams(), PARAM_OBJECT_ID);
    }
}
