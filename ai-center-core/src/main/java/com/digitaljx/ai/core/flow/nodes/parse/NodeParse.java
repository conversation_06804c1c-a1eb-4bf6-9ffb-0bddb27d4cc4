package com.digitaljx.ai.core.flow.nodes.parse;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.node.NodeConfig.OutputParam;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.provider.ModelProvider;
import com.digitaljx.common.core.exception.ServiceException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NodeParse extends FlowNode {


    public FlowResult run(FlowContext context) {
        List<OutputParam> outputParams = this.getConfig().getOutputParams();
        Map<String, Object> model = (Map<String, Object>) this.getConfig().getOptions().get("model");

        // 获得输入参数
        Map<String, Object> params = this.getInputParams();

        // 获取模型
        Map<String, Object> modelParams = MapUtil.get(model, "params", new cn.hutool.core.lang.TypeReference<>() {});
        Long modelId = MapUtil.getLong(modelParams, "modelId");
        if (modelId == null) {
            throw new ServiceException("参数错误，请重新保存");
        }
        ChatLanguageModel llm =  SpringUtil.getBean(ModelProvider.class).text(modelId);
        PromptTemplate userPromptTemplate = PromptTemplate.from("input: {{inputData}} format: {{format}}");
        Map<String, Object> map = Map.of("inputData", params.get("text"),
            "format", getFormat(outputParams));
        Prompt userPrompt = userPromptTemplate.apply(map);
        PromptTemplate sysPromptTemplate = new PromptTemplate("您现在正在充当信息提取工具。当你收到任何输入时，你需要从中提取相关信息，并以请求的JSON格式输出提取的信息，而不回复任何其他无关的内容。");
        Prompt sysPrompt = sysPromptTemplate.apply(Map.of());

        ChatResponse res = llm.chat(sysPrompt.toSystemMessage(), new UserMessage("user", userPrompt.text()));

        // 获取执行结果
        Map<String, Object> extractResult = extractJSON(res.aiMessage().text());

        for (OutputParam param : outputParams) {
            if ("result".equals(param.getField())) {
                context.set(getPrefix() + "." + param.getField(), extractResult, "output");
            } else {
                context.set(getPrefix() + "." + param.getField(), extractResult.get(param.getField()), "output");
            }
        }

        // 更新计数器
        context.updateCount("tokenUsage", res.tokenUsage().totalTokenCount());

        return new FlowResult(true, extractResult);
    }


    private String getFormat(List<OutputParam> list) {
        return list.stream()
            .filter(param -> !"result".equals(param.getField()))
            .map(param -> "\"" + param.getField() + "\": \"" + param.getType() + "\"")
            .reduce((a, b) -> a + "," + b)
            .orElse("");
    }

    private Map<String, Object> extractJSON(String str) {
        // 使用正则表达式匹配JSON字符串
        Pattern jsonRegex = Pattern.compile("\\{(?:[^{}]|(?:\\{[^{}]*\\}))*\\}");
        Matcher matcher = jsonRegex.matcher(str);
        if (matcher.find()) {
            String jsonString = matcher.group();
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                return objectMapper.readValue(jsonString, new TypeReference<Map<String, Object>>() {});
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }
}
