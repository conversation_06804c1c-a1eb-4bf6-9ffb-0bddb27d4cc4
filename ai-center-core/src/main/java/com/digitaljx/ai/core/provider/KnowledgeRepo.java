package com.digitaljx.ai.core.provider;

import com.digitaljx.biz.domain.vo.AiEmbedStoreVo;
import com.digitaljx.biz.domain.vo.AiKnowledgeVo;
import com.digitaljx.biz.domain.vo.AiModelVo;
import com.digitaljx.biz.mapper.AiEmbedStoreMapper;
import com.digitaljx.biz.mapper.AiKnowledgeMapper;
import com.digitaljx.biz.mapper.AiModelMapper;
import com.digitaljx.common.redis.utils.RedisUtils;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static com.digitaljx.common.core.constant.CacheConstants.LOCAL_CACHE_KNOWLEDGE;

/**
 * <AUTHOR>
 * @since 2024/10/29
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KnowledgeRepo {

    private final AiKnowledgeMapper aiKnowledgeMapper;
    private final AiEmbedStoreMapper embedStoreMapper;
    private final AiModelMapper modelMapper;

    private LoadingCache<Long, AiKnowledgeVo> knowledgeMap;

    @Async
    @PostConstruct
    public void init() {
        knowledgeMap = Caffeine.newBuilder()
            .maximumSize(50000)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(this::loadFromDB);

        RedisUtils.subscribe(LOCAL_CACHE_KNOWLEDGE, String.class,
            msg -> {
                log.info("订阅通道 => {}, 接收值 => {}", LOCAL_CACHE_KNOWLEDGE, msg);
                knowledgeMap.invalidateAll();
            });
    }

    private AiKnowledgeVo loadFromDB(Long knowledgeId) {
        AiKnowledgeVo aiKnowledgeVo = aiKnowledgeMapper.selectVoById(knowledgeId);
        if (aiKnowledgeVo == null) {
            return null;
        }
        AiEmbedStoreVo embedStoreVo = embedStoreMapper.selectVoById(aiKnowledgeVo.getEmbedStoreId());
        if (embedStoreVo != null) {
            aiKnowledgeVo.setEmbedStore(embedStoreVo);
        }
        AiModelVo modelVo = modelMapper.selectVoById(aiKnowledgeVo.getEmbedModelId());
        if (modelVo != null) {
            aiKnowledgeVo.setEmbedModel(modelVo);
        }
        return aiKnowledgeVo;
    }

    public AiKnowledgeVo getKnowledge(Long knowledgeId) {
        return knowledgeMap.get(knowledgeId);
    }

}
