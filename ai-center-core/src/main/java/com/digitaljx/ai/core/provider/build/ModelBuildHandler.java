package com.digitaljx.ai.core.provider.build;

import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.image.ImageModel;
import dev.langchain4j.model.scoring.ScoringModel;

/**
 * <AUTHOR>
 * @since 2024-08-18 09:57
 */
public interface ModelBuildHandler {

    /**
     * 判断是不是当前模型
     */
    boolean whetherCurrentModel(AiModel model);

    /**
     * basic check
     */
    default boolean basicCheck(AiModel model) {
        return true;
    }

    /**
     * streaming chat build
     */
    default StreamingChatLanguageModel buildStreamingChat(AiModel model) {
        throw new ServiceException("不支持该类模型");
    }

    /**
     * chat build
     */
    default ChatLanguageModel buildChatLanguageModel(AiModel model) {
        throw new ServiceException("不支持该类模型");
    }

    /**
     * embedding config
     */
    default EmbeddingModel buildEmbedding(AiModel model) {
        throw new ServiceException("不支持该类模型");
    }

    /**
     * image config
     */
    default ImageModel buildImage(AiModel model) {
        throw new ServiceException("不支持该类模型");
    }

    default ScoringModel buildRerankModel(AiModel model) {
        return null;
    }

}
