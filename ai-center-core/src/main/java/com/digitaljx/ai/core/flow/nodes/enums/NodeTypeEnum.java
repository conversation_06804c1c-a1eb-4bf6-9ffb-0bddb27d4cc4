package com.digitaljx.ai.core.flow.nodes.enums;

import java.util.Arrays;

/**
 * 节点类型
 */
public enum NodeTypeEnum {
    start,   // 开始节点
    llm,     // LLM大模型节点
    code,    // 代码执行器节点
    judge,   // 判断器节点
    classify,// 分类节点
    know,    // 知识库节点
    flow,    // 流程节点
    parse,   // 智能解析节点
    variable,// 变量节点
    end,     // 结束节点
    card,    // 卡片节点
    collector, // 信息收集器节点
    api,     // API节点,
    sql,     // SQL节点,
    ;
    public static NodeTypeEnum findEnumByName(String name) {
        return Arrays.stream(values())
            .filter(type -> type.name().equalsIgnoreCase(name))
            .findFirst().orElse(null);
    }
}
