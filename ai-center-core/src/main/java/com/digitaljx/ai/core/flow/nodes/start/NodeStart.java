package com.digitaljx.ai.core.flow.nodes.start;

import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.node.NodeConfig.InputParam;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.common.core.exception.ServiceException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 开始节点
 */
public class NodeStart extends FlowNode {


    /**
     * 执行
     */
    public FlowResult run(FlowContext context) {
        List<InputParam> inputParams = this.getConfig().getInputParams();
        // 获得请求参数(带值的)
        Map<String, Object> requestParams = context.getRequestParams();
        Map<String, Object> result = new HashMap<>();
        // 校验并设置输出参数
        for (InputParam param : inputParams) {
            String paramName = param.getName();
            boolean required = param.isRequired();
            Object value = requestParams.get(paramName);

            if(required && isEmptyValue(value)) {
                throw new ServiceException("参数 " + paramName + " 为必填");
            }
            boolean checkType = checkType(value, param.getType());
            if (value != null && !checkType) {
                throw new ServiceException("参数 " + paramName + " 类型错误");
            }
            value = transformValue(value, param.getType());
            result.put(param.getField(), value);
            context.set(this.getPrefix() + "." + paramName, value, "output");
        }
        return new FlowResult(true, result);
    }

    /**
     * 判断是否为空值
     * @param value
     * @returns
     */
    private boolean isEmptyValue(Object value) {
        // 检查是否是 null
        if (value == null) {
            return true;
        }

        // 特别对待非容器类型（如数字和布尔值）
        if (value instanceof Number || value instanceof Boolean) {
            return false;
        }

        // 对字符串、数组、对象使用相应的判断
        if (value instanceof String) {
            return ((String) value).isEmpty();
        } else if (value instanceof Map) {
            return ((Map<?, ?>) value).isEmpty();
        } else if (value instanceof Iterable) {
            return !((Iterable<?>) value).iterator().hasNext();
        }

        return false;
    }

    /**
     * 转换值
     * @param value
     * @param type
     */
    private Object transformValue(Object value, String type) {
        if ("number".equals(type)) {
            return Double.valueOf(value.toString());
        }
        return value;
    }

    /**
     * 字段类型
     * @param value
     * @returns
     */
    private boolean checkType(Object value, String type) {
        if ("image".equals(type)) {
            return value instanceof String && ((String) value).startsWith("http");
        }
        if ("text".equals(type)) {
            return value instanceof String;
        }
        if ("number".equals(type)) {
            try {
                Double.parseDouble(value.toString());
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return true;
    }
}
