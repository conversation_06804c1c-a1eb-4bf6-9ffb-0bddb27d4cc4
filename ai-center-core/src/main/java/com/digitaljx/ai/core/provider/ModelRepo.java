package com.digitaljx.ai.core.provider;

import com.digitaljx.ai.core.provider.build.ModelBuildHandler;
import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.biz.mapper.AiModelMapper;
import com.digitaljx.common.ai.enums.ModelTypeEnum;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.image.ImageModel;
import dev.langchain4j.model.scoring.ScoringModel;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Async;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2024/6/16
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
public class ModelRepo {

    private final AiModelMapper modelMapper;
    private final List<ModelBuildHandler> modelBuildHandlers;
    private LoadingCache<Long, StreamingChatLanguageModel> streamingChatMap;
    private LoadingCache<Long, ChatLanguageModel> chatLanguageMap;
    private LoadingCache<Long, EmbeddingModel> embeddingModelMap;
    private LoadingCache<Long, ImageModel> imageModelMap;
    private LoadingCache<Long, ScoringModel> scoringModelMap;

    @Async
    @PostConstruct
    public void init() {
        streamingChatMap = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.DAYS)
            .build(this::loadStreamingChatLanguageModel);

        chatLanguageMap = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.DAYS)
            .build(this::loadChatLanguageModel);

        embeddingModelMap = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.DAYS)
            .build(this::loadEmbeddingModel);

        imageModelMap = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.DAYS)
            .build(this::loadImageModel);

        scoringModelMap = Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(30, TimeUnit.DAYS)
            .build(this::loadScoringModel);
    }

    private ScoringModel loadScoringModel(Long modelId) {
        AiModel model = modelMapper.selectById(modelId);
        if (model == null) {
            return null;
        }
        if (Objects.equals(model.getBaseUrl(), "")) {
            model.setBaseUrl(null);
        }
        String type = model.getType();
        if (!ModelTypeEnum.RERANK.name().equals(type)) {
            return null;
        }
        return modelBuildHandlers.stream().filter(h -> h.whetherCurrentModel(model))
            .findAny()
            .map(h -> h.buildRerankModel(model))
            .orElse(null);
    }

    private StreamingChatLanguageModel loadStreamingChatLanguageModel(Long modelId) {
        AiModel model = modelMapper.selectById(modelId);
        if (model == null) {
            return null;
        }
        if (Objects.equals(model.getBaseUrl(), "")) {
            model.setBaseUrl(null);
        }
        String type = model.getType();
        if (!ModelTypeEnum.CHAT.name().equals(type)) {
            return null;
        }
        return modelBuildHandlers.stream().filter(h -> h.whetherCurrentModel(model))
            .findAny()
            .map(h -> h.buildStreamingChat(model))
            .orElse(null);
    }

    private ChatLanguageModel loadChatLanguageModel(Long modelId) {
        AiModel model = modelMapper.selectById(modelId);
        if (model == null) {
            return null;
        }
        if (Objects.equals(model.getBaseUrl(), "")) {
            model.setBaseUrl(null);
        }
        String type = model.getType();
        if (!ModelTypeEnum.CHAT.name().equals(type)) {
            return null;
        }
        return modelBuildHandlers.stream().filter(h -> h.whetherCurrentModel(model))
            .findAny()
            .map(h -> h.buildChatLanguageModel(model))
            .orElse(null);
    }

    private EmbeddingModel loadEmbeddingModel(Long modelId) {
        AiModel model = modelMapper.selectById(modelId);
        if (model == null) {
            return null;
        }
        if (Objects.equals(model.getBaseUrl(), "")) {
            model.setBaseUrl(null);
        }
        String type = model.getType();
        if (!ModelTypeEnum.EMBEDDING.name().equals(type)) {
            return null;
        }
        return modelBuildHandlers.stream().filter(h -> h.whetherCurrentModel(model))
            .findAny()
            .map(h -> h.buildEmbedding(model))
            .orElse(null);
    }

    private ImageModel loadImageModel(Long modelId) {
        AiModel model = modelMapper.selectById(modelId);
        if (model == null) {
            return null;
        }
        if (Objects.equals(model.getBaseUrl(), "")) {
            model.setBaseUrl(null);
        }
        String type = model.getType();
        if (!ModelTypeEnum.TEXT_IMAGE.name().equals(type)) {
            return null;
        }
        return modelBuildHandlers.stream().filter(h -> h.whetherCurrentModel(model))
            .findAny()
            .map(h -> h.buildImage(model))
            .orElse(null);
    }

    public StreamingChatLanguageModel getStreamingChatModel(Long modelId) {
        return streamingChatMap.get(modelId);
    }

    public ChatLanguageModel getChatLanguageModel(Long modelId) {
        return chatLanguageMap.get(modelId);
    }

    public EmbeddingModel getEmbeddingModel(Long modelId) {
        return embeddingModelMap.get(modelId);
    }

    public ImageModel getImageModel(Long modelId) {
        return imageModelMap.get(modelId);
    }

    public ScoringModel getScoringModel(Long modelId) {
        return scoringModelMap.get(modelId);
    }

}
