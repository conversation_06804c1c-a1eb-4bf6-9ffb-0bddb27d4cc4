package com.digitaljx.ai.core.flow.nodes.code;

import cn.hutool.core.util.ReflectUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.node.NodeConfig.OutputParam;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.codehaus.janino.SimpleCompiler;

import java.util.List;
import java.util.Map;

/**
 * 代码执行器节点
 */
@Slf4j
public class NodeCode extends FlowNode {

    /**
     * 执行方法
     *
     * @param context 流程上下文
     * @return 执行结果
     */
    @Override
    public FlowResult run(FlowContext context) {
        Map<String, Object> params = this.getInputParams();
        Map<String, Object> execResult = exec((String) getConfig().getOptions().get("code"), params);
        List<OutputParam> outputParams = getConfig().getOutputParams();
        for (OutputParam param : outputParams) {
            context.set(getPrefix() + "." + param.getField(), execResult.get(param.getField()), "output");
        }
        return new FlowResult(true, execResult);
    }

    /**
     * 执行代码
     * @param script 代码内容
     * @param params 输入参数
     * @return 执行结果
     */
    private Map<String, Object> exec(String script, Map<String, Object> params) {
        try{
            SimpleCompiler compiler = new SimpleCompiler();
            compiler.cook(script);
            ClassLoader classLoader = compiler.getClassLoader();
            Class<?> dynamicClass = classLoader.loadClass("DynamicClass");

            // 实例化并调用方法
            Object instance = dynamicClass.getDeclaredConstructor().newInstance();
            return ReflectUtil.invoke(instance, "main", params);
        } catch (Exception e) {
            log.error("execute script error", e);
            throw new ServiceException(e.getMessage());
        }
    }
}
