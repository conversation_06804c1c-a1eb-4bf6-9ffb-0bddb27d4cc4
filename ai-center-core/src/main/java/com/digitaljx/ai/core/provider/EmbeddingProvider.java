package com.digitaljx.ai.core.provider;

import com.digitaljx.biz.domain.vo.AiKnowledgeVo;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2024/3/8
 */
@Slf4j
@Component
@AllArgsConstructor
public class EmbeddingProvider {

    private final EmbeddingStoreRepo embeddingStoreRepo;
    private final KnowledgeRepo knowledgeRepo;
    private final ModelRepo modelRepo;

    /**
     * 每段300字符，重叠20字符
     * @return
     */
    public static DocumentSplitter splitter() {
        return DocumentSplitters.recursive(300, 20);
    }

    public EmbeddingModel getEmbeddingModel(List<Long> knowledgeIds) {
        List<Long> storeIds = new ArrayList<>();
        for (Long knowledgeId : knowledgeIds) {
            AiKnowledgeVo data = knowledgeRepo.getKnowledge(knowledgeId);
            if (data == null) {
                continue;
            }
            if (data.getEmbedModelId() != null) {
                storeIds.add(data.getEmbedModelId());
            }
        }

        if (storeIds.isEmpty()) {
            throw new ServiceException("知识库缺少Embedding Model配置，请先检查配置");
        }

        HashSet<Long> filterIds = new HashSet<>(storeIds);
        if (filterIds.size() > 1) {
            throw new ServiceException("存在多个不同Embedding Model的知识库，请先检查配置");
        }

        return modelRepo.getEmbeddingModel(storeIds.get(0));
    }

    public EmbeddingModel getEmbeddingModel(Long knowledgeId) {
        AiKnowledgeVo knowledge = knowledgeRepo.getKnowledge(knowledgeId);
        if (knowledge != null) {
            EmbeddingModel embeddingModel = modelRepo.getEmbeddingModel(knowledge.getEmbedModelId());
            if (embeddingModel != null) {
                return embeddingModel;
            }
        }
        throw new ServiceException("没有找到匹配的Embedding向量数据库");
    }

    public EmbeddingStore<TextSegment> getEmbeddingStore(Long knowledgeId) {
        AiKnowledgeVo data = knowledgeRepo.getKnowledge(knowledgeId);
        if (data != null) {
            EmbeddingStore<TextSegment> embeddingStore = embeddingStoreRepo.getEmbeddingStore(data.getEmbedStoreId());
            if (embeddingStore != null) {
                return embeddingStore;
            }
        }
        throw new ServiceException("没有找到匹配的Embedding向量数据库");
    }

    public EmbeddingStore<TextSegment> getEmbeddingStore(List<Long> knowledgeIds) {
        Set<Long> storeIds = new HashSet<>();
        knowledgeIds.forEach(id -> {
            AiKnowledgeVo data = knowledgeRepo.getKnowledge(id);
            if (data != null) {
                if (data.getEmbedStoreId() != null) {
                    storeIds.add(data.getEmbedStoreId());
                }
            }
        });
        if (storeIds.isEmpty()) {
            throw new ServiceException("知识库缺少Embedding Store配置，请先检查配置");
        }

        if (storeIds.size() > 1) {
            throw new ServiceException("存在多个不同Embedding Store数据源的知识库，请先检查配置");
        }

        Long storeId = new ArrayList<>(storeIds).get(0);
        return embeddingStoreRepo.getEmbeddingStore(storeId);
    }

}
