package com.digitaljx.ai.core.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.digitaljx.ai.core.flow.nodes.llm.IChatMemoryAssistant;
import com.digitaljx.ai.core.provider.EmbeddingProvider;
import com.digitaljx.ai.core.provider.ModelProvider;
import com.digitaljx.ai.core.service.LangChainService;
import com.digitaljx.common.ai.dto.ChatReq;
import com.digitaljx.common.ai.dto.ImageR;
import com.digitaljx.common.ai.utils.PromptUtil;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.data.image.Image;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.image.ImageModel;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.rag.DefaultRetrievalAugmentor;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.rag.content.retriever.EmbeddingStoreContentRetriever;
import dev.langchain4j.rag.query.Query;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.TokenStream;
import dev.langchain4j.store.embedding.filter.Filter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.function.Function;

import static com.digitaljx.common.ai.constant.EmbedConst.KNOWLEDGE;
import static dev.langchain4j.store.embedding.filter.MetadataFilterBuilder.metadataKey;

/**
 * <AUTHOR>
 * @since 2024/3/8
 */
@Slf4j
@Service
@AllArgsConstructor
public class LangChainServiceImpl implements LangChainService {

    private final ModelProvider provider;
    private final EmbeddingProvider embeddingProvider;

    private AiServices<IChatMemoryAssistant> build(StreamingChatLanguageModel streamModel, ChatLanguageModel model, ChatReq req) {
        AiServices<IChatMemoryAssistant> aiServices = AiServices.builder(IChatMemoryAssistant.class)
                .chatMemoryProvider(memoryId -> MessageWindowChatMemory.builder()
                        .id(req.getConversationId())
                        .chatMemoryStore(new CustomRedisChatMemoryStore())
                        .maxMessages(20)
                        .build());
        if (StrUtil.isNotBlank(req.getPromptText())) {
            aiServices.systemMessageProvider(memoryId -> req.getPromptText());
        }
        if (streamModel != null) {
            aiServices.streamingChatLanguageModel(streamModel);
        }
        if (model != null) {
            aiServices.chatLanguageModel(model);
        }
        return aiServices;
    }

    @Override
    public TokenStream chat(ChatReq req) {
        StreamingChatLanguageModel model = provider.stream(Long.valueOf(req.getModelId()));
        if (StrUtil.isBlank(req.getConversationId())) {
            req.setConversationId(IdUtil.simpleUUID());
        }

        AiServices<IChatMemoryAssistant> aiServices = build(model, null, req);

//        if (StrUtil.isNotBlank(req.getKnowledgeId())) {
//            req.getKnowledgeIds().add(req.getKnowledgeId());
//        }

        if (req.getKnowledgeIds() != null && !req.getKnowledgeIds().isEmpty()) {
            Function<Query, Filter> filter = (query) -> metadataKey(KNOWLEDGE).isIn(req.getKnowledgeIds());
            ContentRetriever contentRetriever = EmbeddingStoreContentRetriever.builder()
                    .embeddingStore(embeddingProvider.getEmbeddingStore(req.getKnowledgeIds()))
                    .embeddingModel(embeddingProvider.getEmbeddingModel(req.getKnowledgeIds()))
                    .dynamicFilter(filter)
                    .build();
            aiServices.retrievalAugmentor(DefaultRetrievalAugmentor
                    .builder()
                    .contentRetriever(contentRetriever)
                    .build());
        }
        IChatMemoryAssistant agent = aiServices.build();
        return agent.stream(req.getConversationId(), req.getMessage());
    }

    @Override
    public TokenStream singleChat(ChatReq req) {
        StreamingChatLanguageModel model = provider.stream(Long.valueOf(req.getModelId()));
        if (StrUtil.isBlank(req.getConversationId())) {
            req.setConversationId(IdUtil.simpleUUID());
        }

        IChatMemoryAssistant agent = build(model, null, req).build();
        if (req.getPrompt() == null) {
            req.setPrompt(PromptUtil.build(req.getMessage(), req.getPromptText()));
        }
        return agent.stream(req.getConversationId(), req.getPrompt().text());
    }

    @Override
    public String text(ChatReq req) {
        if (StrUtil.isBlank(req.getConversationId())) {
            req.setConversationId(IdUtil.simpleUUID());
        }

        try {
            ChatLanguageModel model = provider.text(Long.parseLong(req.getModelId()));
            IChatMemoryAssistant agent = build(null, model, req).build();
            return agent.text(req.getConversationId(), req.getMessage());
        } catch (Exception e) {
            log.error("text error", e);
            return null;
        }
    }

    @Override
    public Response<Image> image(ImageR req) {
        try {
            ImageModel model = provider.image(Long.valueOf(req.getModelId()));
            return model.generate(req.getPrompt().text());
        } catch (Exception e) {
            log.error("图片生成失败", e);
            throw new ServiceException("图片生成失败");
        }
    }
}
