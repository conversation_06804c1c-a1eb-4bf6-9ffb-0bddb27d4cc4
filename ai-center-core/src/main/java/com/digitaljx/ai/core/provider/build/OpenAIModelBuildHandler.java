package com.digitaljx.ai.core.provider.build;

import cn.hutool.core.util.StrUtil;
import com.digitaljx.common.core.enums.ProviderEnum;
import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.image.ImageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiEmbeddingModel;
import dev.langchain4j.model.openai.OpenAiImageModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;

import static com.digitaljx.common.core.constant.Constants.API_KEY_IS_NULL;

/**
 * <AUTHOR>
 * @since 2024-08-19 10:08
 */
@Slf4j
@Component
public class OpenAIModelBuildHandler implements ModelBuildHandler {

    /**
     * 合并处理支持OpenAI接口的模型
     */
    @Override
    public boolean whetherCurrentModel(AiModel model) {
        String provider = model.getProvider();
        return ProviderEnum.OPENAI.name().equals(provider) ||
            ProviderEnum.GEMINI.name().equals(provider) ||
            ProviderEnum.CLAUDE.name().equals(provider) ||
            ProviderEnum.AZURE_OPENAI.name().equals(provider) ||
            ProviderEnum.DOUYIN.name().equals(provider) ||
            ProviderEnum.YI.name().equals(provider) ||
            ProviderEnum.SILICON.name().equals(provider) ||
            ProviderEnum.DEEPSEEK.name().equals(provider) ||
            ProviderEnum.SPARK.name().equals(provider)
            ;
    }

    @Override
    public boolean basicCheck(AiModel model) {
        String apiKey = model.getApiKey();
        if (StrUtil.isBlank(apiKey)) {
            throw new ServiceException(
                String.format(API_KEY_IS_NULL, model.getProvider().toUpperCase(), model.getType()));
        }
        return true;
    }

    @Override
    public StreamingChatLanguageModel buildStreamingChat(AiModel model) {
        try {
            if (!whetherCurrentModel(model)) {
                return null;
            }
            if (!basicCheck(model)) {
                return null;
            }
            return OpenAiStreamingChatModel
                .builder()
                .apiKey(model.getApiKey())
                .baseUrl(model.getBaseUrl())
                .modelName(model.getModelName())
                .maxTokens(model.getResponseLimit())
                .temperature(model.getTemperature())
                .logRequests(true)
                .logResponses(true)
                .topP(model.getTopP())
                .timeout(Duration.ofMinutes(10))
                .build();
        } catch (ServiceException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(model.getProvider() + " Streaming Chat 模型配置报错", e);
            return null;
        }
    }

    @Override
    public ChatLanguageModel buildChatLanguageModel(AiModel model) {
        try {
            if (!whetherCurrentModel(model)) {
                return null;
            }
            if (!basicCheck(model)) {
                return null;
            }
            return OpenAiChatModel
                .builder()
                .apiKey(model.getApiKey())
                .baseUrl(model.getBaseUrl())
                .modelName(model.getModelName())
                .maxTokens(model.getResponseLimit())
                .temperature(model.getTemperature())
                .logRequests(true)
                .logResponses(true)
                .topP(model.getTopP())
                .timeout(Duration.ofMinutes(10))
                .build();
        } catch (ServiceException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(model.getProvider() + " Chat 模型配置报错", e);
            return null;
        }
    }

    @Override
    public EmbeddingModel buildEmbedding(AiModel model) {
        try {
            if (!whetherCurrentModel(model)) {
                return null;
            }
            if (!basicCheck(model)) {
                return null;
            }
            OpenAiEmbeddingModel openAiEmbeddingModel = OpenAiEmbeddingModel
                .builder()
                .apiKey(model.getApiKey())
                .baseUrl(model.getBaseUrl())
                .modelName(model.getModelName())
                .dimensions(model.getDimension())
                .logRequests(true)
                .logResponses(true)
                .dimensions(1024)
                .timeout(Duration.ofMinutes(10))
                .build();
            return openAiEmbeddingModel;
        } catch (ServiceException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(model.getProvider() + " Embedding 模型配置报错", e);
            return null;
        }
    }

    @Override
    public ImageModel buildImage(AiModel model) {
        try {
            if (!whetherCurrentModel(model)) {
                return null;
            }
            if (!basicCheck(model)) {
                return null;
            }
            return OpenAiImageModel
                .builder()
                .apiKey(model.getApiKey())
                .baseUrl(model.getBaseUrl())
                .modelName(model.getModelName())
                .size(model.getImageSize())
                .quality(model.getImageQuality())
                .style(model.getImageStyle())
                .logRequests(true)
                .logResponses(true)
                .timeout(Duration.ofMinutes(10))
                .build();
        } catch (ServiceException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error(model.getProvider() + " Image 模型配置报错", e);
            return null;
        }


    }
}
