package com.digitaljx.ai.core.flow.nodes;

import cn.hutool.core.util.ObjectUtil;
import com.digitaljx.ai.core.flow.nodes.api.NodeApi;
import com.digitaljx.ai.core.flow.nodes.card.NodeCard;
import com.digitaljx.ai.core.flow.nodes.classify.NodeClassify;
import com.digitaljx.ai.core.flow.nodes.code.NodeCode;
import com.digitaljx.ai.core.flow.nodes.collector.NodeCollector;
import com.digitaljx.ai.core.flow.nodes.end.NodeEnd;
import com.digitaljx.ai.core.flow.nodes.enums.NodeTypeEnum;
import com.digitaljx.ai.core.flow.nodes.flow.NodeFlow;
import com.digitaljx.ai.core.flow.nodes.judge.NodeJudge;
import com.digitaljx.ai.core.flow.nodes.know.NodeKnow;
import com.digitaljx.ai.core.flow.nodes.llm.NodeLLM;
import com.digitaljx.ai.core.flow.nodes.parse.NodeParse;
import com.digitaljx.ai.core.flow.nodes.sql.NodeSql;
import com.digitaljx.ai.core.flow.nodes.start.NodeStart;
import com.digitaljx.ai.core.flow.nodes.variable.NodeVariable;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.common.core.exception.ServiceException;

/**
 * 节点工厂类，负责创建和管理节点实例
 */
public class NodeFactory {

    /**
     * 根据节点类型获取节点实例
     * @param type 节点类型
     * @return 节点实例
     */
    public static FlowNode getNode(String type) {
        NodeTypeEnum typeEnum = NodeTypeEnum.findEnumByName(type);
        if(ObjectUtil.isEmpty(typeEnum)) {
            throw new ServiceException("暂不支持该节点 " + type);
        }
        return switch (typeEnum) {
            case start -> new NodeStart();
            case llm -> new NodeLLM();
            case code -> new NodeCode();
            case judge -> new NodeJudge();
            case classify -> new NodeClassify();
            case know -> new NodeKnow();
            case flow -> new NodeFlow();
            case parse -> new NodeParse();
            case variable -> new NodeVariable();
            case end -> new NodeEnd();
            case card -> new NodeCard();
            case collector -> new NodeCollector();
            case api -> new NodeApi();
            case sql -> new NodeSql();
        };
    }
}
