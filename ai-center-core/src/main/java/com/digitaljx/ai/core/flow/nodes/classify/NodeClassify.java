package com.digitaljx.ai.core.flow.nodes.classify;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.context.FlowGraph;
import com.digitaljx.ai.core.flow.runner.context.LineInfo;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.provider.ModelProvider;
import com.digitaljx.common.core.exception.ServiceException;
import com.digitaljx.common.json.utils.JsonUtils;
import dev.langchain4j.data.message.UserMessage;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.response.ChatResponse;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 分类器
 */
@Slf4j
public class NodeClassify extends FlowNode {

    @Override
    public FlowResult run(FlowContext context) {
        try {
            Map<String, Object> options = this.getConfig().getOptions();
            Map<String, Object> model = (Map<String, Object>) options.get("model");
            List<String> types = (List<String>) options.get("types");

            Map<String, Object> params = this.getInputParams();

            // 获取模型
            Map<String, Object> modelParams = MapUtil.get(model, "params", new cn.hutool.core.lang.TypeReference<>() {});
            Long modelId = MapUtil.getLong(modelParams, "modelId");
            if (modelId == null) {
                throw new ServiceException("参数错误，请重新保存");
            }
            ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
            ChatLanguageModel llm = modelProvider.text(modelId);
            PromptTemplate sysPromptTemplate = getPrompt(types);
            Map<String, Object> variables = Map.of(
                "format", "{\"index\": \"数字类型，分类的序号，如：0\"}"
            );
            Prompt sysPrompt = sysPromptTemplate.apply(variables);

            ChatResponse chatResponse = llm.chat(sysPrompt.toSystemMessage(),
                new UserMessage(params.get("content").toString()));
            return executeChain(chatResponse, types, context);
        } catch (Exception e) {
            log.error("分类器执行异常", e);
            throw new RuntimeException("Execution failed", e);
        }
    }

    /**
     * 获得提示模板
     */
    private PromptTemplate getPrompt(List<String> types) {
        String template = String.format(
            "根据用户的问题，从下面分类中选择一个，并按照JSON格式 {{format}} 返回给我，注意是只要JSON格式，不包含其他格式如 ```json```。序号%s",
            formatListWithIndices(types));
        return new PromptTemplate(template);
    }
    public static String formatListWithIndices(List<String> types) {
        StringBuilder sb = new StringBuilder();
        final int[] index = {0}; // 使用数组来保持可变的索引
        types.forEach(type -> {
            sb.append(index[0]).append(": ").append(type).append("\n");
            index[0]++;
        });
        return sb.toString();
    }

    private FlowResult executeChain(ChatResponse res, List<String> types, FlowContext context) {

        Integer index = extractIndex(res);

        context.set(getPrefix() + ".index", index, "output");
        context.set(getPrefix() + ".content", types.get(index), "output");

        String nextNodeId = nextNodeId(index, context.getFlowGraph());

        context.updateCount("tokenUsage", res.tokenUsage().totalTokenCount());

        FlowResult result = new FlowResult();
        result.setSuccess(true);
        result.setResult(Map.of("index", index, "content", types.get(index)));
        result.setNext(nextNodeId);

        return result;
    }

    private Integer extractIndex(ChatResponse res) {
        try {
            JSONObject result = JSONUtil.parseObj(res.aiMessage().text().replace("```json", "").replace("```", "").trim());
            return Integer.parseInt(result.getStr("index"));
        } catch (Exception e) {
            log.error("JSON解析失败, ai resp:{}, e:{}", JsonUtils.toJsonString(res), e);
            throw new ServiceException("JSON解析失败 " + e.getMessage());
        }
    }

    private String nextNodeId(int index, FlowGraph flowGraph) {
        return flowGraph.getEdges().stream()
            .filter(edge -> edge.getSource().equals(this.getId()) && edge.getSourceHandle().equals("source-" + index))
            .findFirst()
            .map(LineInfo::getTarget)
            .orElse(null);
    }
}
