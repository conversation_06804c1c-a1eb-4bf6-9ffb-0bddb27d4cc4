package com.digitaljx.ai.core.flow.nodes.sql;

import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator;
import com.digitaljx.ai.core.flow.nodes.llm.IChatMemoryAssistant;
import com.digitaljx.ai.core.flow.nodes.llm.IChatAssistant;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.flow.runner.result.FlowStream;
import com.digitaljx.ai.core.provider.ModelProvider;
import com.digitaljx.ai.core.service.impl.CustomRedisChatMemoryStore;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.experimental.rag.content.retriever.sql.SqlDatabaseContentRetriever;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.rag.content.retriever.ContentRetriever;
import dev.langchain4j.service.AiServices;
import org.apache.tika.utils.StringUtils;

import javax.sql.DataSource;
import java.io.IOException;
import java.util.Map;

import static com.digitaljx.common.ai.constant.FlowConst.UN_KNOW_AI_MESSAGE;

/**
 * sql节点
 * <AUTHOR>
 * @since 2025/3/15
 */
public class NodeSql extends FlowNode {
    @Override
    public FlowResult run(FlowContext context) {
        Map<String, Object> options = this.getConfig().getOptions();
        Map<String, Object> model = (Map<String, Object>) options.get("model");
        int history = MapUtil.getInt(options, "history", 0);
        Map<String, Object> params = this.getInputParams();

        final Response<AiMessage>[] res = new Response[]{null};
        try{
            DataSource dataSource = createDataSource();
            if (history > 0) {
                String objectId = context.getObjectId();
                if (StringUtils.isBlank(objectId)) {
                    throw new ServiceException("需要保存历史信息，请求参数必须包含objectId (请添加objectId字段的入参，标记为会话Id)");
                }
                IChatMemoryAssistant chatAssistant = getChatLanguageModel(objectId,
                    model, history, dataSource);
                res[0] = chatAssistant.chatWithSystem(context.getObjectId(), "通过自然语言读取数据库数据", params.get("content").toString());
            } else {
                IChatAssistant chatAssistantWithoutMemory = getChatLanguageModelWithoutMemory(model, dataSource);
                res[0] = chatAssistantWithoutMemory.chatWithSystem( "通过自然语言读取数据库数据", params.get("content").toString());
            }
        } catch (ServiceException e) {
            res[0] = Response.from(UN_KNOW_AI_MESSAGE);
        }

        String respStr = res[0] != null ? res[0].content().text() : null;

        if (context.isStream()) {
            FlowStream flowStreamTmp = context.getFlowStream();
            if (flowStreamTmp == null) {
                flowStreamTmp = new FlowStream();
            }
            FlowStream flowStream = flowStreamTmp;
            try {
                flowStream.getPipedOutputStream().write(respStr.getBytes());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

        context.set(this.getPrefix() + ".text", respStr, "output");
        context.updateCount("tokenUsage", res[0] != null && res[0].tokenUsage() != null ? res[0].tokenUsage().totalTokenCount() : 0);

        FlowResult result = new FlowResult();
        result.setSuccess(true);
        result.setResult(Map.of("text", respStr));
        return result;
    }

    private DataSource createDataSource() {
        Map<String, Object> options = this.getConfig().getOptions();
        String dataSourceType = MapUtil.getStr(options, "dataSourceType");
        String username = MapUtil.getStr(options, "username");
        String password = MapUtil.getStr(options, "password");
        String url = MapUtil.getStr(options, "url");

        DefaultDataSourceCreator dataSourceCreator = SpringUtil.getBean(DefaultDataSourceCreator.class);
        DataSourceProperty dataSourceProperty = new DataSourceProperty();
        dataSourceProperty.setUrl(url);
        dataSourceProperty.setUsername(username);
        dataSourceProperty.setPassword(password);
        dataSourceProperty.setDriverClassName(dataSourceTypeToDriverClassName(dataSourceType));
        return dataSourceCreator.createDataSource(dataSourceProperty);
    }

    private String dataSourceTypeToDriverClassName(String dataSourceType) {
        dataSourceType = dataSourceType.toLowerCase();
        return switch (dataSourceType) {
            case "mysql" -> "com.mysql.jdbc.Driver";
            case "oracle" -> "oracle.jdbc.driver.OracleDriver";
            case "sqlserver" -> "com.microsoft.sqlserver.jdbc.SQLServerDriver";
            case "postgresql" -> "org.postgresql.Driver";
            default -> throw new ServiceException("不支持的数据库类型");
        };
    }

    private IChatMemoryAssistant getChatLanguageModel(String objectId, Map<String, Object> model, int historyCnt, DataSource dataSource) {
        Map<String, Object> params = MapUtil.get(model, "params", new TypeReference<>() {});
        Long modelId = MapUtil.getLong(params, "modelId");
        if (modelId == null) {
            throw new ServiceException("参数错误，请重新保存");
        }
        ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
        ChatLanguageModel llm = modelProvider.text(modelId);

        ContentRetriever contentRetriever = SqlDatabaseContentRetriever.builder()
            .dataSource(dataSource)
            .chatLanguageModel(llm)
            .build();

        AiServices<IChatMemoryAssistant> builder = AiServices.builder(IChatMemoryAssistant.class)
            .chatLanguageModel(llm)
            .contentRetriever(contentRetriever);

        builder.chatMemoryProvider(memoryId -> MessageWindowChatMemory.builder()
            .id(objectId)
            .chatMemoryStore(new CustomRedisChatMemoryStore())
            .maxMessages(historyCnt)
            .build());

        return builder.build();
    }

    private IChatAssistant getChatLanguageModelWithoutMemory(Map<String, Object> model, DataSource dataSource) {
        Map<String, Object> params = MapUtil.get(model, "params", new TypeReference<>() {});
        Long modelId = MapUtil.getLong(params, "modelId");
        if (modelId == null) {
            throw new ServiceException("参数错误，请重新保存");
        }
        ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
        ChatLanguageModel llm = modelProvider.text(modelId);

        ContentRetriever contentRetriever = SqlDatabaseContentRetriever.builder()
            .dataSource(dataSource)
            .chatLanguageModel(llm)
            .build();

        return AiServices.builder(IChatAssistant.class)
            .chatLanguageModel(llm)
            .contentRetriever(contentRetriever).build();
    }
}
