package com.digitaljx.ai.core.provider.build;

import com.digitaljx.common.core.enums.ProviderEnum;
import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.image.ImageModel;
import dev.langchain4j.model.ollama.OllamaChatModel;
import dev.langchain4j.model.ollama.OllamaEmbeddingModel;
import dev.langchain4j.model.ollama.OllamaStreamingChatModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;

import static com.digitaljx.common.core.constant.Constants.BASE_URL_IS_NULL;

/**
 * <AUTHOR>
 * @since 2024-08-19 10:08
 */
@Slf4j
@Component
public class OllamaModelBuildHandler implements ModelBuildHandler {

    @Override
    public boolean whetherCurrentModel(AiModel model) {
        return ProviderEnum.OLLAMA.name().equals(model.getProvider());
    }

    @Override
    public boolean basicCheck(AiModel model) {
        if (StringUtils.isBlank(model.getBaseUrl())) {
            throw new ServiceException(
                String.format(BASE_URL_IS_NULL, ProviderEnum.OLLAMA.name(), model.getType()));
        }
        return true;
    }

    @Override
    public StreamingChatLanguageModel buildStreamingChat(AiModel model) {
        try {
            if (!whetherCurrentModel(model)) {
                return null;
            }
            if (!basicCheck(model)) {
                return null;
            }
            return OllamaStreamingChatModel
                .builder()
                .baseUrl(model.getBaseUrl())
                .modelName(model.getModelName())
                .temperature(model.getTemperature())
                .topP(model.getTopP())
                .logRequests(true)
                .logResponses(true)
                .timeout(Duration.ofMinutes(10))
                .build();
        } catch (ServiceException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Ollama streaming chat 配置报错", e);
            return null;
        }
    }

    @Override
    public ChatLanguageModel buildChatLanguageModel(AiModel model) {
        try {
            if (!whetherCurrentModel(model)) {
                return null;
            }
            if (!basicCheck(model)) {
                return null;
            }
            return OllamaChatModel
                .builder()
                .baseUrl(model.getBaseUrl())
                .modelName(model.getModelName())
                .temperature(model.getTemperature())
                .topP(model.getTopP())
                .logRequests(true)
                .logResponses(true)
                .timeout(Duration.ofMinutes(10))
                .build();
        } catch (ServiceException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Ollama chat 配置报错", e);
            return null;
        }
    }

    @Override
    public EmbeddingModel buildEmbedding(AiModel model) {
        try {
            if (!whetherCurrentModel(model)) {
                return null;
            }
            if (!basicCheck(model)) {
                return null;
            }
            return OllamaEmbeddingModel
                .builder()
                .baseUrl(model.getBaseUrl())
                .modelName(model.getModelName())
                .logRequests(true)
                .logResponses(true)
                .timeout(Duration.ofMinutes(10))
                .build();
        } catch (ServiceException e) {
            log.error(e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Ollama embedding 配置报错", e);
            return null;
        }
    }

}
