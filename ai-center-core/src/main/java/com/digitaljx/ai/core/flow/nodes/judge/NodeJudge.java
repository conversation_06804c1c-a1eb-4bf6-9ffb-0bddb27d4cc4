package com.digitaljx.ai.core.flow.nodes.judge;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.context.FlowGraph;
import com.digitaljx.ai.core.flow.runner.context.LineInfo;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.common.core.exception.ServiceException;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.BiPredicate;

/**
 * 判断器
 */
public class NodeJudge extends FlowNode {

    /**
     * 执行
     *
     * @param context
     */
    public FlowResult run(FlowContext context){
        Options options = JSONUtil.toBean(JSONUtil.toJsonStr(this.getConfig().getOptions()), Options.class);
        List<Condition> conditions = options.getIF();
        Map<String, Object> datas = context.getData("output");
        List<EqResult> eqResults = new ArrayList<>();

        for (Condition item : conditions) {
            String paramValue = (String) datas.get(item.getNodeType() + "." + item.getNodeId() + "." + item.getField());
            String value = item.getValue();
            boolean result = eq(paramValue, value, item.getCondition());
            eqResults.add(new EqResult(result, item.getOperator()));
        }

        boolean finalResult = result(eqResults);
        context.set(getPrefix() + ".result", finalResult, "output");
        String nextNodeId = nextNodeId(context.getFlowGraph(), finalResult);
        return new FlowResult(true, finalResult, nextNodeId);
    }

    /**
     * 下一个节点ID
     * @param flowGraph
     * @param result
     * @returns
     */
    private String nextNodeId(FlowGraph flowGraph, boolean result) {
        // 找到所有的线
        List<LineInfo> edges = flowGraph.getEdges().stream()
            .filter(edge -> edge.getSource().equals(this.getId()))
            .toList();

        // 找到线中sourceHandle为 source-if 或 source-else 的线
        LineInfo edge = edges.stream()
            .filter(e -> e.getSourceHandle().equals(result ? "source-if" : "source-else"))
            .findFirst()
            .orElse(null);

        return edge != null ? edge.getTarget() : null;
    }

    /**
     * 结果
     * @param eqResults
     */
    private boolean result(List<EqResult> eqResults) {
        Boolean finalResult = null;
        for (EqResult item : eqResults) {
            if (finalResult == null) {
                finalResult = item.isResult();
                continue;
            }
            if ("AND".equals(item.getOperator())) {
                finalResult = finalResult && item.isResult();
            } else {
                finalResult = finalResult || item.isResult();
            }
        }
        return finalResult != null && finalResult;
    }

    /**
     * 对比
     * @param paramValue
     * @param value
     * @param condition
     * @returns
     */
    private boolean eq(String paramValue, String value, String condition)  {
        BiPredicate<String, String> method = conditonMethod(condition);
        if (ObjectUtil.isEmpty(method)) {
            throw new ServiceException("Condition method not found: " + condition);
        }
        return method.test(paramValue, value);
    }

    /**
     * 将条件转为具体方法
     * @param condition
     */
    private BiPredicate<String, String> conditonMethod(String condition) {
        return ConditionMethods.getMethod(condition);
    }
}
