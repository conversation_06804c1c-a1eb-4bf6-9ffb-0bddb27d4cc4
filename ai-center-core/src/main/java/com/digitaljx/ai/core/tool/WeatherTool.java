package com.digitaljx.ai.core.tool;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.digitaljx.common.core.exception.ServiceException;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.excel.utils.ExcelUtil;
import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class WeatherTool {

    private static final String apiUrl = "https://restapi.amap.com/v3/weather/weatherInfo?parameters";

    private final String config;

    private static final Map<String, String> CITY_MAP;

    public WeatherTool(String config) {
        this.config = config;
    }

    static {
        InputStream inputStream = ResourceUtil.getStream("amap/AMap_adcode_citycode.xlsx");
        List<AmapImportVo> amapImportVos = ExcelUtil.importExcel(inputStream, AmapImportVo.class);
        log.info("加载高德城市数据" + amapImportVos.size() + "条...");
        CITY_MAP = amapImportVos.stream().collect(Collectors.toMap(AmapImportVo::getAreaName, AmapImportVo::getAdCode));
    }

    /**
     * https://lbs.amap.com/api/webservice/guide/api/weatherinfo
     * @param location
     * @return
     */
    @Tool("获取指定地点的当前天气情况")
    public String getCurrentWeather(
        @P("需要查询的城市名称，例如：北京市、上海市") String location
    ) {
        try {

            JSONObject configJson = JSONUtil.parseObj(config);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("key", configJson.getStr("apiKey"));
            String adCode = CITY_MAP.get(location);
            if (StringUtils.isBlank(adCode)) {
                throw new ServiceException("找不到" + location + "的编码");
            }
            paramMap.put("city", adCode);
            paramMap.put("extensions", "base"); // 可选值：base/all
            String json = HttpUtil.get(apiUrl, paramMap);
            JSONObject respJson = JSONUtil.parseObj(json);

            if ("1".equals(respJson.getStr("status"))) {
                List<JSONObject> lives = respJson.getBeanList("lives", JSONObject.class);
                JSONObject live = lives.get(0);
                String temperature = live.get("temperature").toString() + "℃";
                return String.format("%s当前天气：%s，温度%s，湿度%s%%，风力%s级",
                    live.getStr("province") + live.getStr("city"),
                    live.getStr("weather"),
                    temperature,
                    live.getStr("humidity"),
                    live.getStr("windpower"));
            }
            return "天气查询失败：" + respJson.get("info");
        } catch (Exception e) {
            log.error("高德天气接口调用异常", e);
            return "天气查询服务暂时不可用";
        }
    }

}
