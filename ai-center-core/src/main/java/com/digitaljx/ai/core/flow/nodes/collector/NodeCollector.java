package com.digitaljx.ai.core.flow.nodes.collector;

import cn.hutool.core.map.MapUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.digitaljx.ai.core.flow.nodes.llm.IChatMemoryAssistant;
import com.digitaljx.ai.core.flow.nodes.llm.IChatAssistant;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.flow.runner.result.FlowStream;
import com.digitaljx.ai.core.provider.ModelProvider;
import com.digitaljx.ai.core.service.impl.CustomRedisChatMemoryStore;
import com.digitaljx.common.core.exception.ServiceException;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.service.AiServices;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.utils.StringUtils;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.digitaljx.common.ai.constant.FlowConst.UN_KNOW_AI_MESSAGE;

/**
 * 信息收集器
 * <AUTHOR>
 * @since 2025/3/11
 */
@Slf4j
public class NodeCollector extends FlowNode {
    @Override
    public FlowResult run(FlowContext context) {
        try {
            Map<String, Object> options = this.getConfig().getOptions();
            Map<String, Object> model = (Map<String, Object>) options.get("model");
            List<LinkedHashMap<String, Object>> infos= (List<LinkedHashMap<String, Object>>) options.get("info");

            int history = MapUtil.getInt(options, "history", 0);
            Map<String, Object> params = this.getInputParams();

            PromptTemplate sysPromptTemplate = getPrompt(infos);
            Map<String, Object> variables = Map.of(
                "format", "{\"name\": \"张三\",\"gender\": \"男\"}"
            );
            Prompt sysPrompt = sysPromptTemplate.apply(variables);
            String sysMessage = sysPrompt.text();
            final Response<AiMessage>[] res = new Response[]{null};

            try{
                if (history > 0) {
                    String objectId = context.getObjectId();
                    if (StringUtils.isBlank(objectId)) {
                        throw new ServiceException("需要保存历史信息，请求参数必须包含objectId (请添加objectId字段的入参，标记为会话Id)");
                    }
                    IChatMemoryAssistant chatAssistant = getChatLanguageModel(objectId, model, history);
                    res[0] = chatAssistant.chatWithSystem(context.getObjectId(), sysMessage, params.get("content").toString());
                } else {
                    IChatAssistant chatLanguageModelWithoutMemory = getChatLanguageModelWithoutMemory(model, history);
                    res[0] = chatLanguageModelWithoutMemory.chatWithSystem(sysMessage, params.get("content").toString());

                }

            } catch (ServiceException e) {
                res[0] = Response.from(UN_KNOW_AI_MESSAGE);
            }

            String respStr = res[0] != null ? res[0].content().text() : null;

            if (context.isStream()) {
                FlowStream flowStreamTmp = context.getFlowStream();
                if (flowStreamTmp == null) {
                    flowStreamTmp = new FlowStream();
                }
                FlowStream flowStream = flowStreamTmp;
                try {
                    flowStream.getPipedOutputStream().write(respStr.getBytes());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }

            context.set(this.getPrefix() + ".text", respStr, "output");
            context.updateCount("tokenUsage", res[0] != null && res[0].tokenUsage() != null ? res[0].tokenUsage().totalTokenCount() : 0);

            FlowResult result = new FlowResult();
            result.setSuccess(true);
            result.setResult(Map.of("text", respStr));
            return result;
        } catch (Exception e) {
            log.error("信息收集器执行异常", e);
            throw new RuntimeException("Execution failed", e);
        }
    }

    /**
     * 获得提示模板
     */
    private PromptTemplate getPrompt(List<LinkedHashMap<String, Object>> infos) {
        String template = String.format(
            "通过提问的方式向用户征集以下信息，%s, 一次提问只收集一个信息，如果字段格式错误请提示纠正，直到收集完成时，并按照JSON格式 {{format}} 返回给我，注意是只要JSON格式，不包含其他格式如 ```json```，收集完全部信息前禁止输出任何json。",
            formatListWithIndices(infos));
        return new PromptTemplate(template);
    }

    private static String formatListWithIndices(List<LinkedHashMap<String, Object>> infos) {
        JSONArray arr = new JSONArray();
        for (LinkedHashMap<String, Object> info : infos) {
            JSONObject obj = new JSONObject();
            obj.put("key", info.get("field"));
            obj.put("name", info.get("label"));
            arr.add(obj);
        }
        return arr.toString();
    }

    private IChatMemoryAssistant getChatLanguageModel(String objectId, Map<String, Object> model, int historyCnt) {
        // 获取模型
        Map<String, Object> modelParams = MapUtil.get(model, "params", new cn.hutool.core.lang.TypeReference<>() {});
        Long modelId = MapUtil.getLong(modelParams, "modelId");
        if (modelId == null) {
            throw new ServiceException("参数错误，请重新保存");
        }
        ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
        ChatLanguageModel llm = modelProvider.text(modelId);
        AiServices<IChatMemoryAssistant> builder = AiServices.builder(IChatMemoryAssistant.class);
        builder.chatLanguageModel(llm);

        builder.chatMemoryProvider(memoryId -> MessageWindowChatMemory.builder()
            .id(objectId)
            .chatMemoryStore(new CustomRedisChatMemoryStore())
            .maxMessages(historyCnt)
            .build());
        return builder.build();
    }

    private IChatAssistant getChatLanguageModelWithoutMemory(Map<String, Object> model, int historyCnt) {
        // 获取模型
        Map<String, Object> modelParams = MapUtil.get(model, "params", new cn.hutool.core.lang.TypeReference<>() {});
        Long modelId = MapUtil.getLong(modelParams, "modelId");
        if (modelId == null) {
            throw new ServiceException("参数错误，请重新保存");
        }
        ModelProvider modelProvider = SpringUtil.getBean(ModelProvider.class);
        ChatLanguageModel llm = modelProvider.text(modelId);
        AiServices<IChatAssistant> builder = AiServices.builder(IChatAssistant.class);
        builder.chatLanguageModel(llm);
        return builder.build();
    }
}
