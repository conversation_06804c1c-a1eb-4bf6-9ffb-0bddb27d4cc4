package com.digitaljx.ai.core.service.impl;

import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.redis.utils.RedisUtils;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.data.message.ChatMessageDeserializer;
import dev.langchain4j.data.message.ChatMessageSerializer;
import dev.langchain4j.store.memory.chat.ChatMemoryStore;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Collections;
import java.util.List;

import static dev.langchain4j.internal.ValidationUtils.ensureNotEmpty;

/**
 * 基于Redis的会话存储实现
 *
 * <AUTHOR>
 * @since 2024/8/15
 */
@Slf4j
public class CustomRedisChatMemoryStore implements ChatMemoryStore {

    private static final String CHAT_MEMORY_KEY = "chat:memory:id";


    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        String json = RedisUtils.getCacheObject(CHAT_MEMORY_KEY + memoryId);
        log.info("get redis memory:{}", json);
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }
        return ChatMessageDeserializer.messagesFromJson(json);
    }

    @Override
    public void updateMessages(Object memoryId, List<ChatMessage> messages) {
        String json = ChatMessageSerializer.messagesToJson(ensureNotEmpty(messages, "messages"));
        log.info("put redis memory:{}", json);
        RedisUtils.setCacheObject(CHAT_MEMORY_KEY + memoryId, json, Duration.ofDays(1));
    }

    @Override
    public void deleteMessages(Object memoryId) {
        RedisUtils.deleteObject(CHAT_MEMORY_KEY + memoryId);
    }

}
