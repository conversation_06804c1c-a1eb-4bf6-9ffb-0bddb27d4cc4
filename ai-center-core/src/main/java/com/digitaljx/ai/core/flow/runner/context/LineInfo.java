package com.digitaljx.ai.core.flow.runner.context;
import lombok.Getter;
import lombok.Setter;

/**
 * 线信息
 */
@Getter
@Setter
public class LineInfo {

    // 线的唯一标识符
    private String id;

    // 连接的目标节点的标识符
    private String target;

    // 连接的类型，可选
    private String type;

    // 连接的源节点的标识符
    private String source;

    // 目标节点的处理器标识符，可空
    private String targetHandle;

    // 源节点的处理器标识符，可空
    private String sourceHandle;

    // 是否动画化
    private Boolean animated;

    // 线的样式，任意类型
    private Object style;
}
