package com.digitaljx.ai.core.tool;

import dev.langchain4j.agent.tool.P;
import dev.langchain4j.agent.tool.Tool;

import java.time.DateTimeException;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2025/3/20
 */
public class TimeTool {

    @Tool("获取当前时间（默认上海时区，可指定其他时区）")
    public String getCurrentTime(
        @P(value = "时区ID，例如Asia/Shanghai（默认），Europe/Paris", required = false) String timezone
    ) {
        try {
            ZoneId zoneId = (timezone != null && !timezone.isEmpty())
                ? ZoneId.of(timezone)
                : ZoneId.of("Asia/Shanghai"); // 修改默认时区

            return ZonedDateTime.now(zoneId)
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss z"));
        } catch (DateTimeException e) {
            return "无效的时区格式，请使用标准时区ID（例如Asia/Shanghai）";
        }
    }
}
