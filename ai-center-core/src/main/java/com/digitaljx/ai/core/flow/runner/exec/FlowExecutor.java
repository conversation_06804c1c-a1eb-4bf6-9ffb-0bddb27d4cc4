package com.digitaljx.ai.core.flow.runner.exec;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.context.FlowNodeExec;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.ErrorDetail;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 执行器
 */
@Getter
@Setter
@Component
@Slf4j
public class FlowExecutor {

    /**
     * 执行一个节点
     * @param node
     * @param context
     * @param callback
     * @return
     */
    public FlowResult one(FlowNode node, FlowContext context, FlowCallback callback)
        throws IOException {
        long start = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        FlowResult flowResult = node.invoke(context);
        if (callback != null) {
            callback.execute(new FlowCallbackResult(flowResult, node.getId(), end - start, null, context.getCount(), true));
        }
        return flowResult;
    }

    /**
     * 串行执行
     * @param nodes
     * @param context
     * @param callback
     * @return
     */
    public FlowResult serial(List<FlowNode> nodes, FlowContext context, FlowCallback callback)
        throws IOException {
        boolean isEnd = false;
        // 查找开始节点
        FlowNode startNode = nodes.stream().filter(node -> "start".equals(node.getType())).findFirst().orElse(null);
        if (startNode == null) {
            return null;
        }
        // 当前节点
        FlowNode currentNode = this.execNode(nodes, context, startNode.getId());
        // 如果当前节点是 end，则结束
        if ("end".equals(currentNode.getType())) {
            isEnd = true;
        }
        FlowResult result = new FlowResult();
        List<Map<String, Object>> nodesResult = new ArrayList<>();
        while (currentNode != null) {
            long start = System.currentTimeMillis();
            try {
                // 执行当前节点
                result = currentNode.invoke(context);
                saveNodeResult(nodesResult, currentNode, result);
                // 更新当前节点信息
                FlowNodeExec flowNodeExec = context.getFlowNodeExec();
                flowNodeExec.setPrev(flowNodeExec.getCurrent());
                flowNodeExec.setCurrent(flowNodeExec.getNext());
                context.setFlowNodeExec(flowNodeExec);
                // 如果当前节点是 end，则结束
                if ("end".equals(currentNode.getType())) {
                    isEnd = true;
                    flowNodeExec.setNext(null);
                }
            } catch (Exception e) {
                result = new FlowResult(false, new ErrorDetail(currentNode.getId(), e.getMessage(), null, currentNode.getConfig().getOptions()));
                log.error("执行节点出错", e);
                saveNodeResult(nodesResult, currentNode, result);
                isEnd = true;
            }
            long end = System.currentTimeMillis();
            if (callback != null) {
                callback.execute(new FlowCallbackResult(result, currentNode.getId(), end - start, context.getFlowNodeExec().getNext(), context.getCount(), isEnd));
            }
            if (isEnd) {
                break;
            }
            currentNode = execNode(nodes, context, result.getNext());
        }
        result.setNodesResult(nodesResult);
        return result;
    }

    public void saveNodeResult(List<Map<String, Object>> nodesResult, FlowNode currentNode, FlowResult result) {
        Map<String, Object> nodeResult = BeanUtil.beanToMap(currentNode);
        nodeResult.remove("context");
        if (ObjectUtil.isNotEmpty(result)) {
            nodeResult.putAll(BeanUtil.beanToMap(result));
        }
        nodesResult.add(nodeResult);
    }

    /**
     * 获得要执行的节点
     * @param nodes
     * @param context
     * @param nextId
     * @return
     */
    public FlowNode execNode(List<FlowNode> nodes, FlowContext context, String nextId) {
        FlowNodeExec flowNodeExec = context.getFlowNodeExec();
        String execId;

        if ("end".equals(flowNodeExec.getCurrent())) {
            return null;
        }

        if (nextId != null) {
            execId = nextId;
            findAndSetNext(context, nextId);
        } else {
            findAndSetNext(context, flowNodeExec.getCurrent());
            execId = flowNodeExec.getCurrent();
        }

        return nodes.stream().filter(node -> execId.equals(node.getId())).findFirst().orElse(null);
    }

    private void findAndSetNext(FlowContext context, String source) {
        FlowNodeExec flowNodeExec = context.getFlowNodeExec();
        List<String> nextNodeIds = context.getFlowGraph().getEdges().stream()
            .filter(line -> source.equals(line.getSource()))
            .map(line -> line.getTarget())
            .collect(Collectors.toList());
        if (nextNodeIds.isEmpty()) {
            return;
        }
        flowNodeExec.setCurrent(source);
        flowNodeExec.setNextList(nextNodeIds);
        if (nextNodeIds.size() == 1) {
            flowNodeExec.setNext(nextNodeIds.get(0));
        }
        context.setFlowNodeExec(flowNodeExec);
    }

    @FunctionalInterface
    public interface FlowCallback {
        void execute(FlowCallbackResult result) throws IOException;
    }

    @Getter
    @Setter
    public static class FlowCallbackResult {
        private FlowResult result;
        private String nodeId;
        private long duration;
        private String nextNodeId;
        private Object count;
        private boolean isEnd;

        public FlowCallbackResult(FlowResult result, long duration, boolean isEnd) {
            this.result = result;
            this.duration = duration;
            this.isEnd = isEnd;
        }

        public FlowCallbackResult(FlowResult result, String nodeId, long duration, String nextNodeId, Object count, boolean isEnd) {
            this.result = result;
            this.nodeId = nodeId;
            this.duration = duration;
            this.nextNodeId = nextNodeId;
            this.count = count;
            this.isEnd = isEnd;
        }

    }
}
