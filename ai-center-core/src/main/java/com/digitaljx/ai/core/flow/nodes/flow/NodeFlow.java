package com.digitaljx.ai.core.flow.nodes.flow;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.node.NodeConfig.OutputParam;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.service.FlowRunService;
import com.digitaljx.biz.domain.vo.AiAppVo;
import com.digitaljx.biz.service.IAiAppService;
import com.digitaljx.common.core.exception.ServiceException;

import java.util.List;
import java.util.Map;

/**
 * 流程节点
 */
public class NodeFlow extends FlowNode {

    /**
     * 执行方法
     *
     * @param context 流程上下文
     * @return 执行结果
     */
    @Override
    public FlowResult run(FlowContext context) {
        Map<String, Object> params = this.getInputParams();
        Object result;

        // 获取流程label
        AiAppVo flowInfo = SpringUtil.getBean(IAiAppService.class).queryById(
            (Long) this.getConfig().getOptions().get("flowId"));
        if(flowInfo == null) {
            throw new ServiceException("流程不存在");
        }

        // 执行流程
        result = SpringUtil.getBean(FlowRunService.class).invoke(params, flowInfo.getLabel(), false).getResult();
        List<OutputParam> outputParams = this.getConfig().getOutputParams();
        // 设置输出参数
        for (OutputParam param : outputParams) {
            if (ObjectUtil.isEmpty(param.getNodeType())) {
                param.setNodeType(this.getType());
            }
            if (ObjectUtil.isEmpty(param.getNodeId())) {
                param.setNodeId(this.getId());
            }
            if (result instanceof Map) {
                context.set(getParamPrefix(param) + "." + param.getField(),
                    ((Map) result).get(param.getField()), "output");
            } else {
                context.set(getParamPrefix(param) + "." + param.getField(),
                    ReflectUtil.getFieldValue(result, param.getField()), "output");
            }
        }
        return new FlowResult(true, result);
    }
}
