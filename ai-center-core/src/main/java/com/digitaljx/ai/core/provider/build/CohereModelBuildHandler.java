package com.digitaljx.ai.core.provider.build;

import com.digitaljx.biz.domain.AiModel;
import com.digitaljx.common.core.enums.ProviderEnum;
import dev.langchain4j.model.cohere.CohereScoringModel;
import dev.langchain4j.model.scoring.ScoringModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/3/19
 */
@Slf4j
@Component
public class CohereModelBuildHandler implements ModelBuildHandler {
    @Override
    public boolean whetherCurrentModel(AiModel model) {
        return ProviderEnum.COHERE.name().equals(model.getProvider());
    }

    @Override
    public ScoringModel buildRerankModel(AiModel model) {
        return CohereScoringModel.builder()
            .apiKey(model.getApiKey())
            .modelName(model.getModelName())
            .build();
    }
}
