package com.digitaljx.ai.core.flow.runner.result;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 结果
 */
@Getter
@Setter
public class FlowResult {
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 异常信息(如果有则返回)
     */
    private ErrorDetail error;

    /**
     * 返回结果
     */
    private Object result;

    /**
     * 所有已经执行的节点信息
     */
    private List<Map<String, Object>> nodesResult;

    /**
     * 下个节点，如果有多个下个节点，当前节点需要做出判断选择一个节点执行
     */
    private String next;

    /**
     * stream流
     */
    private FlowStream stream;

    public FlowResult() {
    }
    public FlowResult(ErrorDetail error) {
        this.error = error;
    }

    public FlowResult(boolean success, ErrorDetail error) {
        this.success = success;
        this.error = error;
    }

    public FlowResult(boolean success, Object result) {
        this.success = success;
        this.result = result;
    }

    public FlowResult(boolean success, Object result, FlowStream stream) {
        this.success = success;
        this.result = result;
        this.stream = stream;
    }
    public FlowResult(boolean success, Object result, String next) {
        this.success = success;
        this.result = result;
        this.next = next;
    }
}
