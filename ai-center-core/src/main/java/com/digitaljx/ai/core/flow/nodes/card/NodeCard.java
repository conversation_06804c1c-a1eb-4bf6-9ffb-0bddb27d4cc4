package com.digitaljx.ai.core.flow.nodes.card;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.flow.runner.result.FlowStream;
import com.digitaljx.biz.domain.vo.AiCardVo;
import com.digitaljx.biz.mapper.AiCardMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

/**
 * 卡片
 * <AUTHOR>
 * @since 2025/3/11
 */
@Slf4j
public class NodeCard extends FlowNode {

    @Override
    public FlowResult run(FlowContext context) {
        Map<String, Object> options = this.getConfig().getOptions();
        Long cardId = Long.parseLong(options.get("cardId").toString());
        AiCardMapper cardMapper = SpringUtil.getBean(AiCardMapper.class);
        AiCardVo cardVo = cardMapper.selectVoById(cardId);
        context.set(this.getPrefix() + ".card", cardVo, "output");
        if (context.isStream()) {
            FlowStream flowStreamTmp = context.getFlowStream();
            if (flowStreamTmp == null) {
                flowStreamTmp = new FlowStream();
            }
            FlowStream flowStream = flowStreamTmp;
            try {
                flowStream.getPipedOutputStream().write(JSON.toJSONString(cardVo).getBytes());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

        }
        return new FlowResult(true, cardVo);
    }
}
