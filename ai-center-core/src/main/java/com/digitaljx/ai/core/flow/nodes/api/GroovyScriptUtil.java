package com.digitaljx.ai.core.flow.nodes.api;

import cn.hutool.crypto.SecureUtil;
import com.digitaljx.common.core.exception.ServiceException;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import groovy.lang.GroovyRuntimeException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor;

import java.util.concurrent.TimeUnit;

@Slf4j
public class GroovyScriptUtil {

    public static final Cache<String, GroovyObject> CACHE_GROOVYS = Caffeine.newBuilder()
            .maximumSize(50000)
            .expireAfterWrite(4, TimeUnit.HOURS)
            .build();

    public static GroovyClassLoader groovyClassLoader;

    static {
        ClassLoader parent = AutowiredAnnotationBeanPostProcessor.class.getClassLoader();
        groovyClassLoader = new GroovyClassLoader(parent);
    }

    public static GroovyObject loadScript(String script) {
        return CACHE_GROOVYS.get(getKey(script), (s) -> getGroovyObject(script));
    }

    @SneakyThrows
    private static String getKey(String script) {
        return SecureUtil.md5(script);
    }

    private static GroovyObject getGroovyObject(String script) {
        try {
            Class groovyClass = groovyClassLoader.parseClass(script);
            return (GroovyObject) groovyClass.newInstance();
        } catch (Exception e) {
            log.error("loadScript error", e);
            throw new ServiceException("脚本加载异常");
        }
    }

    public static Object invokeMethod(GroovyObject object, String method, Object[] args) {
        try {
            return object.invokeMethod(method, args);
        } catch (GroovyRuntimeException e) {
            log.error("脚本执行异常", e);
            throw new ServiceException(e.getMessage());
        } catch (Exception e) {
            log.error("脚本执行异常", e);
            throw new ServiceException("脚本执行异常");
        }
    }

    public static Object invokeMethod(String script, String method, Object[] args) {
        GroovyObject groovy = loadScript(script);
        if (groovy != null) {
            return invokeMethod(groovy, method, args);
        } else {
            return null;
        }
    }

}
