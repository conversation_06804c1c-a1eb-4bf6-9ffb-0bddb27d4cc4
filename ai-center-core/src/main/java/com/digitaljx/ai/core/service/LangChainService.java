package com.digitaljx.ai.core.service;

import com.digitaljx.common.ai.dto.ChatReq;
import com.digitaljx.common.ai.dto.ImageR;
import dev.langchain4j.data.image.Image;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.service.TokenStream;

/**
 * <AUTHOR>
 * @since 2024/3/8
 */
public interface LangChainService {

    /**
     * 带知识库
     * @param req
     * @return
     */
    TokenStream chat(ChatReq req);

    /**
     * 不带知识库
     * @param req
     * @return
     */
    TokenStream singleChat(ChatReq req);

    String text(ChatReq req);

    Response<Image> image(ImageR req);
}
