package com.digitaljx.ai.core.flow.nodes.know;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.context.NodeInfo;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.ai.core.service.KnowRetrieverService;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.rag.content.aggregator.ContentAggregator;
import dev.langchain4j.rag.query.router.QueryRouter;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.digitaljx.ai.core.flow.nodes.enums.NodeTypeEnum.llm;
import static com.digitaljx.common.ai.constant.FlowConst.RAG_MIN_SCORE;
import static com.digitaljx.common.ai.constant.FlowConst.RAG_RETRIEVE_NUMBER_DEFAULT;

/**
 * 知识库节点
 */
@Slf4j
public class NodeKnow extends FlowNode {

    /**
     * 执行
     * @param context
     * @return
     */
    @Override
    public FlowResult run(FlowContext context) {
        Map<String, Object> options = this.getConfig().getOptions();
        List<Long> knowIds = BeanUtil.copyToList((Collection<?>) options.get("knowIds"), Long.class);
        Map<String, Object> params = this.getInputParams();
        String text = (String) params.get("text");
        Object object = options.get("size");
        Integer maxResults = ObjectUtil.isEmpty(object) ? RAG_RETRIEVE_NUMBER_DEFAULT : (Integer) object;
        Object minScoreObj = options.get("minScore");
        Double minScore = ObjectUtil.isEmpty(minScoreObj) ? RAG_MIN_SCORE : (Double) minScoreObj;
        String next = context.getFlowNodeExec().getNext();
        NodeInfo nodeInfo = context.getFlowGraph().getNodes().stream()
            .filter(o -> ObjUtil.equals(o.getId(), next)).findFirst().orElse(null);
        if (ObjUtil.isNotNull(nodeInfo) && ObjUtil.equals(nodeInfo.getType(), llm.name())) {
            // 下一个节点是 大模型，使用RAG增强
            Object breakIfSearchMissedObj = options.get("breakIfSearchMissed");
            boolean breakIfSearchMissed = ObjUtil.equals(breakIfSearchMissedObj, 1);
            KnowRetrieverService knowRetrieverService = SpringUtil.getBean(KnowRetrieverService.class);
            QueryRouter queryRouter = knowRetrieverService
                .getQueryRouter(knowIds, maxResults, minScore, breakIfSearchMissed);
            ContentAggregator contentAggregator = knowRetrieverService.getContentAggregator(knowIds, minScore);
            context.set(this.getPrefix() + ".queryRouter", queryRouter, "input");
            context.set(this.getPrefix() + ".contentAggregator", contentAggregator, "input");
            context.set(this.getPrefix() + ".breakIfSearchMissed", options.get("breakIfSearchMissed"), "input");
            context.set(this.getPrefix() + ".documents", new ArrayList(), "output");
            context.set(this.getPrefix() + ".text", text, "output");
            FlowResult flowResult = new FlowResult(true, Map.of(
                "documents", new ArrayList(),
                "text", text
            ));
            return flowResult;
        }
        // 直接检索向量知识库
        List<EmbeddingMatch<TextSegment>> documents = SpringUtil.getBean(KnowRetrieverService.class).search(knowIds, text, maxResults, minScore);
        String str = documents.stream().filter(o -> ObjectUtil.isNotEmpty(o.embedded())).map(o -> o.embedded().text()).collect(Collectors.joining("\n\r"));
        List<Map<String, Object>> metadataList = documents.stream().filter(o -> ObjectUtil.isNotEmpty(o.embedded())).map(o -> {
            Metadata metadata = o.embedded().metadata();
            return metadata.toMap();
        }).toList();
        context.set(this.getPrefix() + ".documents", metadataList, "output");
        context.set(this.getPrefix() + ".text", str, "output");
        return new FlowResult(true, Map.of(
            "documents", metadataList,
            "text", str
        ));
    }
}
