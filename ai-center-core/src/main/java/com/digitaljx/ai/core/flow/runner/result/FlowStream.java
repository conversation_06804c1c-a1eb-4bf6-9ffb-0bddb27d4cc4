package com.digitaljx.ai.core.flow.runner.result;

import lombok.Getter;
import lombok.Setter;

import java.io.PipedInputStream;
import java.io.PipedOutputStream;

/**
 * 自定义管道流
 */
@Setter
@Getter
public class FlowStream {
    private PipedInputStream pipedInputStream;
    private PipedOutputStream pipedOutputStream;

    public FlowStream() {
    }

    public FlowStream(PipedInputStream pipedInputStream, PipedOutputStream pipedOutputStream) {
        this.pipedInputStream = pipedInputStream;
        this.pipedOutputStream = pipedOutputStream;
    }
}
