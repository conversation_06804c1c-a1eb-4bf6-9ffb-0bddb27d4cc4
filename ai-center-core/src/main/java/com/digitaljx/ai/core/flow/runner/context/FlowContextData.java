package com.digitaljx.ai.core.flow.runner.context;

import cn.hutool.core.map.MapUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 上下文数据
 */
@Getter
@Setter
public class FlowContextData {
    // 输入
    private Map<String, Object> input;
    // 输出
    private Map<String, Object> output;

    public FlowContextData() {
        input = MapUtil.newHashMap();
        output = MapUtil.newHashMap();
    }
}
