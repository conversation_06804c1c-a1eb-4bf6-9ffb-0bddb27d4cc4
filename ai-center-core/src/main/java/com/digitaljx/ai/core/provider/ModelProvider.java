package com.digitaljx.ai.core.provider;

import cn.hutool.core.util.ObjectUtil;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.image.ImageModel;
import dev.langchain4j.model.scoring.ScoringModel;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/3/8
 */
@Component
@AllArgsConstructor
public class ModelProvider {

    private final ModelRepo modelRepo;

    public StreamingChatLanguageModel stream(Long modelId) {
        StreamingChatLanguageModel streamingChatModel = modelRepo.getStreamingChatModel(modelId);
        if (ObjectUtil.isNotEmpty(streamingChatModel)) {
            return streamingChatModel;
        }
        throw new RuntimeException("没有匹配到模型，请检查模型配置！");
    }

    public ChatLanguageModel text(Long modelId) {
        ChatLanguageModel chatLanguageModel = modelRepo.getChatLanguageModel(modelId);
        if (ObjectUtil.isNotEmpty(chatLanguageModel)) {
            return chatLanguageModel;
        }
        throw new RuntimeException("没有匹配到模型，请检查模型配置！");
    }

    public ImageModel image(Long modelId) {
        ImageModel imageModel = modelRepo.getImageModel(modelId);
        if (ObjectUtil.isNotEmpty(imageModel)) {
            return imageModel;
        }
        throw new RuntimeException("没有匹配到模型，请检查模型配置！");
    }

    public ScoringModel scoring(Long modelId) {
        ScoringModel scoringModel = modelRepo.getScoringModel(modelId);
        if (ObjectUtil.isNotEmpty(scoringModel)) {
            return scoringModel;
        }
        throw new RuntimeException("没有匹配到模型，请检查模型配置！");
    }
}
