package com.digitaljx.ai.core.flow.nodes.api;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.digitaljx.ai.core.flow.runner.context.FlowContext;
import com.digitaljx.ai.core.flow.runner.node.FlowNode;
import com.digitaljx.ai.core.flow.runner.result.FlowResult;
import com.digitaljx.common.core.utils.StringUtils;
import com.digitaljx.common.json.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * API调用
 * <AUTHOR>
 * @since 2025/3/12
 */
@Slf4j
public class NodeApi extends FlowNode {
    @Override
    public FlowResult run(FlowContext context) {

//        ApiRequestDTO apiRequestDTO = BeanUtil.toBean(rootNode, ApiRequestDTO.class);
//        log.info("api request:{}", JsonUtils.toJsonString(apiRequestDTO));
//
//        // 替换占位符变量
//        if (StringUtils.isNotBlank(apiRequestDTO.getBody())) {
//            String body = apiRequestDTO.getBody();
//            body = StrUtil.removeAll(body, '\n', '\t');
//            body = StrUtil.trim(body);
//            apiRequestDTO.setBody(StrUtils.format(body, params));
//        }
//        if (CollUtil.isNotEmpty(apiRequestDTO.getQuerys())) {
//            apiRequestDTO.getQuerys().forEach(it -> it.setValue(StrUtils.format(it.getValue(), params)));
//        }
//        if (CollUtil.isNotEmpty(apiRequestDTO.getHeaders())) {
//            apiRequestDTO.getHeaders().forEach(it -> it.setValue(StrUtils.format(it.getValue(), params)));
//        }
//
//        log.info("api after:{}", JsonUtils.toJsonString(apiRequestDTO));
//
//        processBefore(rootNode.getScriptBefore(), apiRequestDTO);
//
//        String requestMethod = rootNode.getRequestMethod();
//
//        List<ApiParam> headers = apiRequestDTO.getHeaders();
//        Map<String, String> headerMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(headers)) {
//            headerMap = headers.stream().filter(it -> StringUtils.isNotBlank(it.getKey()))
//                .collect(Collectors.toMap(ApiParam::getKey, ApiParam::getValue));
//        }
//
//        List<ApiParam> querys = apiRequestDTO.getQuerys();
//        Map<String, Object> queryMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(querys)) {
//            queryMap = querys.stream().filter(it -> StringUtils.isNotBlank(it.getKey()))
//                .collect(Collectors.toMap(ApiParam::getKey, ApiParam::getValue));
//        }
//
//        String body = apiRequestDTO.getBody();
//        String result = null;
//        log.info("api final querys:{}", JsonUtils.toJsonString(queryMap));
//
//        log.info("api final body:{}", body);
//
//        if ("GET".equals(requestMethod)) {
//            result = HttpRequest.get(rootNode.getUrl())
//                .addHeaders(headerMap)
//                .form(queryMap)
//                .execute().body();
//        } else if ("POST".equals(requestMethod)) {
//            result = HttpRequest.post(rootNode.getUrl())
//                .addHeaders(headerMap)
//                .form(queryMap)
//                .body(body)
//                .execute().body();
//        }
//
//        log.info("api resp:{}", result);
//        String res = processAfter(rootNode.getScriptAfter(), result);
//        log.info("api resp after:{}", res);
        return null;
    }

//    private void processBefore(String script, ApiRequestDTO apiRequestDTO) {
//        if (StringUtils.isBlank(script)) {
//            return;
//        }
//        GroovyScriptUtil.invokeMethod(script, "invoke", new Object[]{apiRequestDTO});
//    }
//
//    private String processAfter(String script, String respStr) {
//        if (StringUtils.isBlank(script)) {
//            return respStr;
//        }
//        return (String) GroovyScriptUtil.invokeMethod(script, "invoke", new Object[]{respStr});
//    }
}
