package com.digitaljx.ai.core.flow.runner.node;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 节点配置
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class NodeConfig {

    private List<InputParam> inputParams;
    private List<OutputParam> outputParams;
    private Map<String, Object> options;

    @Getter
    @Setter
    public static class InputParam {
        /** 节点ID */
        private String nodeId;
        /** 参数名 */
        private String name;
        /** 类型(哪个节点) */
        private String nodeType;
        /** 显示名称 */
        private String label;
        /** 字段 */
        private String field;
        /** 值类型 */
        private String type;
        /** 是否必填 */
        private boolean required;
        /** 默认值 */
        private Object defaultValue;
        /** 参数值 */
        private Object value;
    }

    @Getter
    @Setter
    public static class OutputParam {
        /** 节点ID */
        private String nodeId;
        /** 参数名 */
        private String name;
        /** 字段 */
        private String field;
        /** 字段类型 */
        private String type;
        /** 类型(哪个节点) */
        private String nodeType;
    }

}
