package com.digitaljx.ai.core.flow.nodes.llm;

import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.service.*;

public interface IChatAssistant {

    @SystemMessage("{{sm}}")
    TokenStream chatWithSystemStream(@V("sm") String systemMessage, @UserMessage String prompt);

    @SystemMessage("{{sm}}")
    Response<AiMessage> chatWithSystem(@V("sm") String systemMessage, @UserMessage String prompt);
}
