package com.digitaljx.ai.core.provider;

import cn.hutool.core.util.StrUtil;
import com.digitaljx.common.core.enums.EmbedStoreEnum;
import com.digitaljx.biz.domain.vo.AiEmbedStoreVo;
import com.digitaljx.biz.mapper.AiEmbedStoreMapper;
import com.digitaljx.common.core.exception.ServiceException;
import com.digitaljx.common.redis.utils.RedisUtils;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import dev.langchain4j.community.store.embedding.redis.RedisEmbeddingStore;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.milvus.MilvusEmbeddingStore;
import dev.langchain4j.store.embedding.pgvector.PgVectorEmbeddingStore;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

import static com.digitaljx.common.core.constant.CacheConstants.LOCAL_CACHE_EMBED_STORE;

/**
 * <AUTHOR>
 * @since 2024/10/28
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EmbeddingStoreRepo {

    private final AiEmbedStoreMapper embedStoreMapper;

    private LoadingCache<Long, EmbeddingStore<TextSegment>> embedStoreMap;

    @PostConstruct
    public void init() {
        embedStoreMap = Caffeine.newBuilder()
            .maximumSize(50000)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .build(this::loadFromDB);

        RedisUtils.subscribe(LOCAL_CACHE_EMBED_STORE, String.class,
            msg -> {
                log.info("订阅通道 => {}, 接收值 => {}", LOCAL_CACHE_EMBED_STORE, msg);
                embedStoreMap.invalidateAll();
            });
    }

    private EmbeddingStore<TextSegment> loadFromDB(Long id) {
        AiEmbedStoreVo embed = embedStoreMapper.selectVoById(id);
        if (EmbedStoreEnum.REDIS.name().equalsIgnoreCase(embed.getProvider())) {
            RedisEmbeddingStore.Builder builder = RedisEmbeddingStore.builder()
                    .host(embed.getHost())
                    .port(embed.getPort())
                    .indexName(embed.getDatabaseName())
                    .dimension(embed.getDimension());
            if (StrUtil.isNotBlank(embed.getUsername()) && StrUtil.isNotBlank(embed.getPassword())) {
                builder.user(embed.getUsername()).password(embed.getPassword());
            }
            return builder.build();
        }
        if (EmbedStoreEnum.PGVECTOR.name().equalsIgnoreCase(embed.getProvider())) {
            return PgVectorEmbeddingStore.builder()
                    .host(embed.getHost())
                    .port(embed.getPort())
                    .database(embed.getDatabaseName())
                    .dimension(embed.getDimension())
                    .user(embed.getUsername())
                    .password(embed.getPassword())
                    .table(embed.getTableName())
                    .indexListSize(1)
                    .useIndex(true)
                    .createTable(true)
                    .dropTableFirst(false)
                    .build();
        }
        if (EmbedStoreEnum.MILVUS.name().equalsIgnoreCase(embed.getProvider())) {
            return MilvusEmbeddingStore.builder()
                    .host(embed.getHost())
                    .port(embed.getPort())
                    .databaseName(embed.getDatabaseName())
                    .dimension(embed.getDimension())
                    .username(embed.getUsername())
                    .password(embed.getPassword())
                    .collectionName(embed.getTableName())
                    .build();
        }
        throw new ServiceException("暂不支持的类型");
    }

    public EmbeddingStore<TextSegment> getEmbeddingStore(Long embeddingId) {
        return embedStoreMap.get(embeddingId);
    }

}
