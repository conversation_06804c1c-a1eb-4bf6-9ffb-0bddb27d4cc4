package com.digitaljx.ai.core.flow.runner.context;

import com.digitaljx.ai.core.flow.runner.node.NodeConfig;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 节点信息
 */
@Getter
@Setter
public class NodeInfo {

    private Boolean enable;
    private String id;
    private String label;
    private String type;
    private String icon;
    private String name;
    private Position position;
    private Form form;
    private Handle handle;
    private NodeConfig data;

    @Getter
    @Setter
    public static class Position {
        private double x;
        private double y;
    }

    @Getter
    @Setter
    public static class Form {
        private String width;
        private String focus;
        private List<Object> items;
    }

    @Getter
    @Setter
    public static class Handle {
        private Boolean target;
        private Boolean source;
        private List<Next> next;
    }

    @Getter
    @Setter
    public static class Next {
        private String label;
        private String value;
    }
}
